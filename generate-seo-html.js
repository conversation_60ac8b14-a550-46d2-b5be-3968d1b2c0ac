import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import content from './src/pages/seo/seo-content.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const outDir = path.join(__dirname, 'src/pages/seo/content');

async function generate() {
  await fs.rm(outDir, { recursive: true, force: true });
  for (const [slug, data] of Object.entries(content)) {
    const segments = slug.split('/').filter(Boolean);
    const dir = path.join(outDir, ...segments);
    await fs.mkdir(dir, { recursive: true });
    const file = path.join(dir, 'index.html');
    const html = `<h1>${data.title}</h1>\n<p>${data.description}</p>\n`;
    await fs.writeFile(file, html);
  }
}

generate();
