# Realty Game Currency Defaults

This document explains how the default currency for a game is provided by the backend, stored in Pinia, and used by UI components.

## Data flow

- Backend endpoint `/api_public/v4/realty_game_summary/:slug` returns:
  - `realty_game_details.default_game_currency` (e.g., "EUR")
  - (Legacy) `price_guess_inputs.default_currency` may also be present
  - `realty_game_details.*` meta fields

- `useRealtyGame.fetchAndSetGameData(slug, store)` processes the response and calls `store.setRealtyGameData()` with:
  - `gameListings` (filtered by `visible === true`)
  - `gameTitle`, `gameDesc`, `gameBgImageUrl`
  - `gameDefaultCurrency` (prefers `realty_game_details.default_game_currency`, falls back to `price_guess_inputs.default_currency`, defaulting to `GBP`)

- `useRealtyGameStore` state includes `gameDefaultCurrency` and exposes it via getter `getGameDefaultCurrency`.

## Component usage

- `RealtyGameStartPage.vue` computes `storeDefaultCurrency = realtyGameStore.gameDefaultCurrency` and initializes the currency on mount:
  1. Ensures game data is loaded (awaits `initializeGame()`)
  2. Sets the active currency using priority:
     - `sessionData.selectedCurrency`
     - `getCurrencySelection()` (persisted)
     - `storeDefaultCurrency` (from backend)
     - Fallback `'CAD'` as final default

This sequence ensures existing user preference wins, else the backend default is used.

## Common pitfalls

- Race condition: If the component sets currency before the store is populated, `storeDefaultCurrency` may be undefined. We await the data load in `onMounted` to fix this.
- Backend field mismatch: Ensure the composable maps `realty_game_details.default_game_currency` first, and falls back to `price_guess_inputs.default_currency`.

## Tests

- Unit tests verify:
  - Store stores and exposes `gameDefaultCurrency`.
  - `fetchAndSetGameData` maps `default_game_currency` (or legacy `default_currency`) to the store and return value.
  - `RealtyGameStartPage` initializes currency from stored preference or backend default.
