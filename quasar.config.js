/* eslint-env node */

/*
 * This file runs in a Node context (it's NOT transpiled by Babel), so use only
 * the ES6 features that are supported by your Node version. https://node.green/
 */

// Configuration for your app
// https://v2.quasar.dev/quasar-cli-vite/quasar-config-js

import { defineConfig } from '#q-app/wrappers'
import path from 'path'
import { fileURLToPath } from 'url'

import { beasties } from 'vite-plugin-beasties'

// ES modules don't have __dirname, so we need to create it
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)
// Default app folder name if env is not provided
const ROOT_FOLDER_NAME = process.env.ROOT_FOLDER_NAME || 'hpg-main'


// below from https://quasar.dev/quasar-cli-vite/handling-process-env
// This will load from `.env` if it exists, but not override existing `process.env.*` values
// No longer need to import dotenv manually as Quasar v2 has native support

export default defineConfig(function (/* ctx */) {
  return {
    htmlVariables: {
      // titleFromQConfig: titleFromQConfig,
      // descFromQConfig: descFromQConfig,
      serviceName: process.env.SERVICE_NAME,
      srcGitRev: process.env.SRC_GIT_REV,
      productName: process.env.PRODUCT_NAME,
      productDescription: process.env.PRODUCT_DESCRIPTION,
      //  siteConfig.siteTitle,
      // some: {
      //   prop: 'my-prop'
      // }
    },
    // https://v2.quasar.dev/quasar-cli-vite/prefetch-feature
    preFetch: true,

    // app boot file (/src/boot)
    // --> boot files are part of "main.js"
    // https://v2.quasar.dev/quasar-cli-vite/boot-files
    boot: [
      // 'firebase',
      'axios',
      'pwb-flex-conf',
      'calendar',
      // 'chartkick',
      {
        server: false, // run on client-side only!
        path: 'apexcharts',
      },
      {
        server: false, // run on client-side only!
        path: 'open-layers', // references /src/boot/<name>.js
      },
      {
        server: false, // run on client-side only!
        path: 'ahoy',
      },
      {
        server: false, // run on client-side only!
        path: 'tawk-to',
      },
      // {
      //   server: false, // run on client-side only!
      //   path: 'vue-google-maps', // references /src/boot/<name>.js
      // },
    ],

    // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#css
    css: ['app.scss'],

    // https://github.com/quasarframework/quasar/tree/dev/extras
    extras: [
      // 'ionicons-v4',
  'mdi-v7',
      // 'fontawesome-v6',
      // 'eva-icons',
      // 'themify',
      // 'line-awesome',
      // 'roboto-font-latin-ext', // this or either 'roboto-font', NEVER both!

      'roboto-font', // optional, you are not bound to it
      'material-icons', // optional, you are not bound to it
    ],

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#build
    build: {
      target: {
        browser: ['es2019', 'edge88', 'firefox78', 'chrome87', 'safari13.1'],
        node: 'node20',
      },

      vueRouterMode: 'hash', // available values: 'hash', 'history'
      // vueRouterBase,
      // vueDevtools,
      // vueOptionsAPI: false,

      // rebuildCache: true, // rebuilds Vite/linter/etc cache on startup

      // Nov 2024 - below would have caused entire app to be hosted
      // on /q/ path, but I now want to host it on root path again
      // publicPath: '/q',
      // analyze: true,
      env: {
        G_TAG: process.env.G_TAG || '',
        GMAPS_API_KEY: process.env.GMAPS_API_KEY || '',
        PRODUCT_NAME: process.env.PRODUCT_NAME || '',
        ROOT_FOLDER_NAME: process.env.ROOT_FOLDER_NAME || '',
        PRODUCT_DESCRIPTION: process.env.PRODUCT_DESCRIPTION || '',
        TAWK_TO_ENABLED: process.env.TAWK_TO_ENABLED || 'false',
        TAWK_TO_PROPERTY_ID: process.env.TAWK_TO_PROPERTY_ID || '',
        TAWK_TO_WIDGET_ID: process.env.TAWK_TO_WIDGET_ID || '',
      },
      // rawDefine: {}
      // ignorePublicFolder: true,
      // minify: false,
      // polyfillModulePreload: true,
      // distDir

      extendViteConf(viteConf) {
        Object.assign(viteConf.optimizeDeps, {
          include: ['vue-google-maps-community-fork', 'fast-deep-equal'],
        })
        viteConf.plugins = viteConf.plugins || []
        viteConf.plugins.push(
          beasties({
            options: {
              path: 'dist/spa',
              preload: 'swap',
              pruneSource: false, // keep full CSS to avoid losing responsive/grid rules in production
              inlineFonts: true,
              compress: true,
            }
          })
        )
      },
      // viteVuePluginOptions: {},

      vitePlugins: [
        [
          'vite-plugin-checker',
          {
            eslint: {
              lintCommand: 'eslint "./**/*.{js,mjs,cjs,vue}"',
            },
          },
          { server: false },
        ],
      ],
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#devServer
    devServer: {
      // https: true
      open: true, // opens browser window automatically
    },

    // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#framework
    framework: {
      config: {},

      // iconSet: 'material-icons', // Quasar icon set
      // lang: 'en-US', // Quasar language pack

      // For special cases outside of where the auto-import strategy can have an impact
      // (like functional components as one of the examples),
      // you can manually specify Quasar components/directives to be available everywhere:
      //
      // components: [],
      // directives: [],

      // Quasar plugins
      plugins: ['AppFullscreen', 'Notify', 'Dialog', 'Meta', 'Cookies'],
    },

    // animations: 'all', // --- includes all animations
    // https://v2.quasar.dev/options/animations
    animations: [],

    // https://v2.quasar.dev/quasar-cli-vite/quasar-config-js#property-sourcefiles
    sourceFiles: {
      rootComponent: `src/apps/${ROOT_FOLDER_NAME}/App.vue`,
      router: `src/apps/${ROOT_FOLDER_NAME}/router/index`,
      // rootComponent: 'src/App.vue',
      // router: 'src/router/index',
      // store: 'src/store/index',
      // registerServiceWorker: 'src-pwa/register-service-worker',
      // serviceWorker: 'src-pwa/custom-service-worker',
      // pwaManifestFile: 'src-pwa/manifest.json',
      // electronMain: 'src-electron/electron-main',
      // electronPreload: 'src-electron/electron-preload'
    },

    // https://v2.quasar.dev/quasar-cli-vite/developing-ssr/configuring-ssr
    ssr: {
      // ssrPwaHtmlFilename: 'offline.html', // do NOT use index.html as name!
      // will mess up SSR

      // extendSSRWebserverConf (esbuildConf) {},
      // extendPackageJson (json) {},

      pwa: false,

      // manualStoreHydration: true,
      // manualPostHydrationTrigger: true,

      prodPort: 3000, // The default port that the production server should use
      // (gets superseded if process.env.PORT is specified at runtime)

      middlewares: [
        'render', // keep this as last one
      ],
    },

    // https://v2.quasar.dev/quasar-cli-vite/developing-pwa/configuring-pwa
    pwa: {
      workboxMode: 'generateSW', // or 'injectManifest'
      injectPwaMetaTags: true,
      swFilename: 'sw.js',
      manifestFilename: 'manifest.json',
      useCredentialsForManifestTag: false,
      // useFilenameHashes: true,
      // extendGenerateSWOptions (cfg) {}
      // extendInjectManifestOptions (cfg) {},
      // extendManifestJson (json) {}
      // extendPWACustomSWConf (esbuildConf) {}
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-cordova-apps/configuring-cordova
    cordova: {
      // noIosLegacyBuildFlag: true, // uncomment only if you know what you are doing
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-capacitor-apps/configuring-capacitor
    capacitor: {
      hideSplashscreen: true,
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-electron-apps/configuring-electron
    electron: {
      // extendElectronMainConf (esbuildConf)
      // extendElectronPreloadConf (esbuildConf)

      // specify the debugging port to use for the Electron app when running in development mode
      inspectPort: 5858,

      bundler: 'packager', // 'packager' or 'builder'

      packager: {
        // https://github.com/electron-userland/electron-packager/blob/master/docs/api.md#options
        // OS X / Mac App Store
        // appBundleId: '',
        // appCategoryType: '',
        // osxSign: '',
        // protocol: 'myapp://path',
        // Windows only
        // win32metadata: { ... }
      },

      builder: {
        // https://www.electron.build/configuration/configuration

        appId: 'hpg-quasar-app-id',
      },
    },

    // Full list of options: https://v2.quasar.dev/quasar-cli-vite/developing-browser-extensions/configuring-bex
    bex: {
      contentScripts: ['my-content-script'],

      // extendBexScriptsConf (esbuildConf) {}
      // extendBexManifestJson (json) {}
    },
  }
})
