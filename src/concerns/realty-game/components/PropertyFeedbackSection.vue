<template>
  <q-expansion-item
    v-model="showPropertyFeedback"
    icon="comment"
    label="Share your thoughts about this property (optional)"
    class="property-feedback-section q-mt-md"
  >
    <q-card flat bordered class="q-mt-sm">
      <q-card-section class="q-pa-md">
        <div class="text-body2 text-grey-7 q-mb-md">
          Help us improve! What did you think about this property's
          pricing or details?
        </div>

        <q-form @submit="handleSubmit">
          <q-input
            v-model="feedbackText"
            type="textarea"
            outlined
            rows="3"
            placeholder="Your thoughts about this property..."
            :disable="isLoading"
            class="q-mb-md"
          />

          <div class="row q-gutter-sm">
            <q-btn
              type="submit"
              color="primary"
              label="Submit Feedback"
              size="sm"
              :loading="isLoading"
              :disable="!feedbackText.trim()"
            />

            <q-btn
              label="Skip"
              color="grey"
              flat
              size="sm"
              @click="handleSkip"
            />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </q-expansion-item>
</template>

<script>
import { ref, computed } from 'vue'
import { useQuasar } from 'quasar'
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export default {
  name: 'PropertyFeedbackSection',

  props: {
    currentProperty: {
      type: Object,
      required: true,
    },
    currentResult: {
      type: Object,
      default: null,
    },
    playerName: {
      type: String,
      default: '',
    },
    routeSssnId: {
      type: String,
      required: true,
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
  },

  emits: ['update:modelValue', 'feedback-submitted'],

  setup(props, { emit }) {
    const $q = useQuasar()

    // Local reactive state
    const feedbackText = ref('')
    const isLoading = ref(false)

    // Computed property for v-model support
    const showPropertyFeedback = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value),
    })

    // Methods
    const handleSubmit = async () => {
      if (!feedbackText.value.trim()) return

      isLoading.value = true

      try {
        const feedbackData = {
          guessed_price_uuid:
            props.currentResult?.savedEstimate?.guessed_price?.uuid,
          property_uuid: props.currentProperty.uuid,
          property_title:
            props.currentProperty.gl_title_atr || props.currentProperty.title,
          game_session_id: props.routeSssnId,
          player_name: props.playerName,
          feedback_text: feedbackText.value.trim(),
          user_guess: props.currentResult?.guess,
          actual_price: props.currentProperty.price_sale_current_cents / 100,
          score: props.currentResult?.score,
          submitted_at: new Date().toISOString(),
          page_url: window.location.href,
        }

        // Submit to backend API
        const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/forms_ppsq/sp/game_property_feedback`
        await axios.post(apiUrl, {
          property_feedback: feedbackData,
        })

        // Show success message
        $q.notify({
          message: 'Thanks for your feedback!',
          color: 'positive',
          icon: 'check_circle',
          position: 'top',
          timeout: 2000,
        })

        // Reset state and emit events
        feedbackText.value = ''
        showPropertyFeedback.value = false
        emit('feedback-submitted')
      } catch (error) {
        console.error('Failed to submit property feedback:', error)
        $q.notify({
          message: 'Failed to submit feedback. Please try again.',
          color: 'negative',
          icon: 'error',
          position: 'top',
        })
      } finally {
        isLoading.value = false
      }
    }

    const handleSkip = () => {
      showPropertyFeedback.value = false
    }

    return {
      showPropertyFeedback,
      feedbackText,
      isLoading,
      handleSubmit,
      handleSkip,
    }
  },
}
</script>

<style scoped>
.property-feedback-section {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  .property-feedback-section {
    margin: 0.5rem 0;
  }

  .property-feedback-section :deep(.q-expansion-item__header) {
    padding: 0.75rem;
    font-size: 0.875rem;
    line-height: 1.3;
    word-wrap: break-word;
    white-space: normal;
  }

  .property-feedback-section :deep(.q-expansion-item__label) {
    white-space: normal;
    overflow-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
    flex: 1;
    min-width: 0;
  }

  .property-feedback-section :deep(.q-expansion-item__container) {
    padding: 0;
  }
}
</style>
