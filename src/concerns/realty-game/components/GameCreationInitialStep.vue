<template>
  <div class="game-creation-initial-step">
    <q-form @submit="handleSubmit" class="q-gutter-md">
      <!-- User Information -->
      <q-card class="modern-card q-py-lg">
        <q-card-section>
          <div class="section-header q-mb-md">
            <q-icon name="person" color="primary" size="md" class="q-mr-sm" />
            <h6 class="q-mt-none q-mb-none text-h6 text-weight-bold">
              Your Information
            </h6>
          </div>

          <q-input
            v-model="form.email"
            label="Email Address *"
            type="email"
            outlined
            class="modern-input"
            :rules="emailRules"
            hint="We'll use this to notify you about your game"
          />
        </q-card-section>
      </q-card>

      <!-- Property Listing URL -->
      <q-card class="modern-card q-py-lg">
        <q-card-section>
          <div class="section-header q-mb-md">
            <q-icon name="link" color="primary" size="md" class="q-mr-sm" />
            <h6 class="q-mt-none q-mb-none text-h6 text-weight-bold">
              Property Listing
            </h6>
          </div>

          <q-input
            v-model="form.propertyUrl"
            label="Property Listing URL *"
            type="url"
            outlined
            class="modern-input"
            :rules="urlRules"
            hint="Valid listing URL (e.g., from Rightmove, Zoopla, etc.)"
          />

          <div v-if="form.propertyUrl && isValidUrl" class="q-mt-md">
            <q-banner class="modern-banner bg-green-1 text-green-8">
              <template v-slot:avatar>
                <q-icon name="check_circle" color="green" />
              </template>
              Great! Looks like a valid listing URL.
            </q-banner>
          </div>
        </q-card-section>
      </q-card>

   

      <!-- Action Buttons -->
      <div class="row justify-center q-gutter-md q-my-lg">
        <!-- <q-btn
          flat
          color="grey-7"
          label="Cancel"
          size="lg"
          class="cta-secondary"
          @click="$emit('cancel')"
        /> -->
        <q-btn
          type="submit"
          color="primary"
          label="Continue"
          icon="arrow_forward"
          size="lg"
          class="cta-primary"
          :loading="loading"
          :disable="!isFormValid"
        />
      </div>


         <!-- Information Card -->
      <q-card class="modern-card q-py-lg bg-blue-1">
        <q-card-section>
          <div class="row items-center q-mb-md">
            <q-icon name="info" color="blue" size="md" class="q-mr-sm" />
            <h6
              class="q-mt-none q-mb-none text-h6 text-weight-bold text-blue-8"
            >
              What happens next?
            </h6>
          </div>

          <div class="text-blue-8">
            <p class="q-mb-sm">
              After providing your email and a property listing URL, you'll be
              able to:
            </p>
            <ul class="q-pl-md">
              <li>Set up your game title and description</li>
              <!-- <li>Configure game schedule and settings</li>
              <li>Add more property listings to your game</li> -->
              <li>Share your custom game with friends</li>
            </ul>
          </div>
        </q-card-section>
      </q-card>
    </q-form>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  initialData: {
    type: Object,
    default: () => ({}),
  },
})

// Emits
const emit = defineEmits(['submit', 'cancel'])

// Form data
const form = ref({
  email: '',
  propertyUrl: '',
  ...props.initialData,
})

// Validation rules
const emailRules = [
  (val) => !!val || 'Email is required',
  (val) => /.+@.+\..+/.test(val) || 'Please enter a valid email address',
]

const urlRules = [
  (val) => !!val || 'Property URL is required',
  (val) => {
    try {
      new URL(val)
      return true
    } catch {
      return 'Please enter a valid URL'
    }
  },
  // (val) => {
  //   const validDomains = [
  //     'rightmove.co.uk',
  //     'zoopla.co.uk',
  //     'onthemarket.com',
  //     'primelocation.com',
  //     'spareroom.co.uk',
  //     'openrent.co.uk',
  //     'propertypal.com',
  //     's1homes.com',
  //     'daft.ie',
  //     'myhome.ie',
  //   ]

  //   try {
  //     const url = new URL(val)
  //     const hostname = url.hostname.toLowerCase()
  //     const isValid = validDomains.some((domain) => hostname.includes(domain))
  //     return (
  //       isValid ||
  //       'Please use a URL from a recognized property website (Rightmove, Zoopla, etc.)'
  //     )
  //   } catch {
  //     return 'Please enter a valid URL'
  //   }
  // },
]

// Computed properties
const isFormValid = computed(() => {
  return (
    form.value.email &&
    form.value.propertyUrl &&
    emailRules.every((rule) => rule(form.value.email) === true) &&
    urlRules.every((rule) => rule(form.value.propertyUrl) === true)
  )
})

const isValidUrl = computed(() => {
  if (!form.value.propertyUrl) return false
  return urlRules.every((rule) => rule(form.value.propertyUrl) === true)
})

// Methods
const handleSubmit = () => {
  emit('submit', form.value)
}
</script>

<style scoped>
/* Modern Cards */
.modern-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.section-header {
  display: flex;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
}

/* Modern Form Elements */
.modern-input {
  border-radius: 12px;
}

.modern-banner {
  border-radius: 12px;
  border-left: 4px solid #4caf50;
}

/* CTA Buttons */
.cta-primary {
  box-shadow: 0 8px 25px rgba(156, 39, 176, 0.3);
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 1.1rem;
}

.cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(156, 39, 176, 0.4);
}

.cta-secondary {
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 1.1rem;
}

.cta-secondary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(156, 39, 176, 0.2);
}
</style>
