<template>
  <div class="rg-results-detailed-content q-mb-xl q-pb-xl">
    <!-- Results Header -->
    <div class="results-header q-mb-xl text-center">
      <h1 class="text-h4 text-weight-bold text-primary q-mb-lg">
        {{
          isCurrentUserSession
            ? `Well done ${ssGameSession.game_player_nickname}, challenge complete`
            : `${ssGameSession.game_player_nickname}'s challenge results`
        }}
      </h1>

      <!-- Session Info and Overall Ranking -->
      <div class="session-info q-mb-md">
        <template v-if="overallRanking">
          <q-chip
            v-if="overallRanking.rank === 1"
            color="amber-7"
            text-color="black"
            icon="military_tech"
            size="lg"
            class="q-mb-sm text-h6 text-weight-bold shadow-2"
            style="font-size: 1.2em; padding: 0.7em 1.5em"
          >
            🥇
            {{
              isCurrentUserSession
                ? 'Congratulations! You are the'
                : `${ssGameSession.game_player_nickname} is
            the`
            }}
            <span class="text-weight-bold">&nbsp;Top Player</span> &nbsp;(1st of
            {{ overallRanking.total_sessions }})
          </q-chip>
          <q-chip
            v-else-if="overallRanking.rank === 2"
            color="blue-grey-2"
            text-color="black"
            icon="military_tech"
            size="lg"
            class="q-mb-sm text-h6 text-weight-bold shadow-2"
            style="font-size: 1.2em; padding: 0.7em 1.5em"
          >
            🥈
            {{
              isCurrentUserSession
                ? 'Amazing! You ranked'
                : `${ssGameSession.game_player_nickname} ranked`
            }}
            <span class="text-weight-bold">&nbsp;2nd&nbsp;</span> of
            {{ overallRanking.total_sessions }}
          </q-chip>
          <q-chip v-else color="info" text-color="white" icon="emoji_events">
            {{
              isCurrentUserSession
                ? 'You ranked'
                : `${ssGameSession.game_player_nickname} ranked`
            }}
            {{ overallRanking.rank }} of
            {{ overallRanking.total_sessions }}
          </q-chip>
        </template>
      </div>

      <div class="performance-badge q-mb-md">
        <q-icon
          :name="playerResults.performance_rating?.icon"
          :color="playerResults.performance_rating?.color"
          size="4em"
        />
        <div
          class="text-h4 text-weight-bold q-mt-md"
          :class="`text-${playerResults.performance_rating?.color}`"
        >
          {{ playerResults.performance_rating?.rating }}
        </div>
        <div class="text-h6 text-grey-7">
          {{ playerResults.total_score }} /
          {{ playerResults.max_possible_score }} points
        </div>
      </div>

      <div>
        <SocialSharing
          socialSharingPrompt="Challenge your friends and see how they compare!
              Share your results without revealing the actual property prices."
          socialSharingTitle="Check out how I did in this property price game"
          :urlProp="shareableResultsUrl"
        ></SocialSharing>

        <div>
          <a :href="shareableResultsUrl">{{ shareableResultsUrl }}</a>
        </div>
      </div>
      <p class="text-body1 text-grey-7">
        <!-- Here's how you performed on each property -->
      </p>
    </div>

  

    <div class="q-mt-md"></div>
    <!-- Combined Results Summary -->
    <GuessingGameResultsSummary
      :rows="gameBreakdown"
      :columns="resultsColumns"
      :comparison-data="comparisonSummary"
      :title="
        isCurrentUserSession
          ? 'Your Complete Game Results'
          : `${ssGameSession.game_player_nickname}'s Complete Game Results`
      "
      title-icon="leaderboard"
      row-key="uuid"
      :pagination="{ rowsPerPage: 0 }"
      :hide-bottom="gameBreakdown.length < 10"
      property-link-route="rPriceGameProperty"
      :property-link-params="(row) => ({ listingInGameUuid: row.listing_uuid })"
      :format-guess="formatGuessPrice"
      :format-actual="formatActualPrice"
      :get-score-color="getScoreColor"
      :format-price="formatPriceWithBothCurrencies"
      :is-current-user-session="isCurrentUserSession"
      :player-name="ssGameSession.game_player_nickname"
      :show-prices="isCurrentUserSessionOrUuidRoute"
      :show-table="true"
      :show-comparison="true"
      :show-overall-stats="true"
      score-display-type="chip"
    />

  <!-- Leaderboard Section -->
  <GuessingGameLeadingScores
    :leadingGameScores="leadingGameScores"
    :show-leaderboard="true"
    :columns="leaderboardColumns"
    title="Leaderboard"
  />
    <!-- Results Table -->
    <!-- <ResultsTable :rows="gameBreakdown"
                  :columns="resultsColumns"
                  :title="isCurrentUserSession ? 'How you performed on each property' : `How ${ssGameSession.game_player_nickname} performed on each property`"
                  title-icon="leaderboard"
                  row-key="uuid"
                  :pagination="{ rowsPerPage: 0 }"
                  :hide-bottom="gameBreakdown.length < 10"
                  property-link-route="rPriceGameProperty"
                  :property-link-params="(row) => ({ listingInGameUuid: row.listing_uuid })"
                  :format-guess="formatGuessPrice"
                  :format-actual="formatActualPrice"
                  :get-score-color="getScoreColor"
                  score-display-type="chip" /> -->

    <!-- Comparison Summary -->
    <!-- <GuessingGameCompSummary :comparison-data="comparisonSummary"
                             :is-current-user-session="isCurrentUserSession"
                             :player-name="ssGameSession.game_player_nickname"
                             :show-prices="isCurrentUserSession" /> -->

    <!-- Community Details Section -->
    <q-no-ssr>
      <div
        v-if="gameCommunitiesDetailsCalc.show"
        class="results-actions q-pa-md text-center bg-grey-2 rounded-borders shadow-2"
      >
        <q-card-section class="text-center">
          <div class="text-h6 q-mb-sm">🎮 Join the Community Discussion</div>
          <q-btn
            :label="gameCommunitiesDetailsCalc.redditCommunity.url"
            :href="gameCommunitiesDetailsCalc.redditCommunity.url"
            type="a"
            color="red"
            target="_blank"
            icon="mdi-reddit"
            flat
          />
        </q-card-section>

        <q-card-section>
          <div class="text-subtitle1 q-mb-sm text-center">
            🧠 Other Price Guess Games You Might Like
          </div>
          <q-list bordered separator class="rounded-borders">
            <q-item
              v-for="(
                relevantGame, index
              ) in gameCommunitiesDetailsCalc.relevantGames"
              :key="index"
              clickable
            >
              <q-item-section>
                <a
                  :href="`https://${relevantGame}`"
                  target="_blank"
                  class="text-primary"
                >
                  {{ `https://${relevantGame}` }}
                </a>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>
      </div>
    </q-no-ssr>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import SocialSharing from 'src/concerns/dossiers/components/sharing/SocialSharing.vue'
// import ResultsTable from 'src/components/common/ResultsTable.vue'
// import GuessingGameCompSummary from 'src/components/common/GuessingGameCompSummary.vue'
import GuessingGameResultsSummary from 'src/components/common/GuessingGameResultsSummary.vue'
import GuessingGameLeadingScores from 'src/components/common/GuessingGameLeadingScores.vue'

const route = useRoute()

const props = defineProps({
  results: {
    type: Object,
    required: true,
  },
  playerResults: {
    type: Object,
    required: true,
  },
  ssGameSession: {
    type: Object,
    required: true,
  },
  overallRanking: {
    type: Object,
    default: null,
  },
  leadingGameScores: {
    type: Array,
    default: () => [],
  },
  gameBreakdown: {
    type: Array,
    required: true,
  },
  comparisonSummary: {
    type: Array,
    required: true,
  },
  isCurrentUserSession: {
    type: Boolean,
    required: true,
  },
  // showLdrboard: {
  //   type: Boolean,
  //   default: false,
  // },
  formatPriceWithBothCurrencies: {
    type: Function,
    required: true,
  },
  getScoreColor: {
    type: Function,
    required: true,
  },
  gameCommunitiesDetailsCalc: {
    type: Object,
    required: true,
  },
  shareableResultsUrl: {
    type: String,
    required: true,
  },
})

// Leaderboard columns
const leaderboardColumns = [
  {
    name: 'session_guest_name',
    label: 'Player',
    field: 'session_guest_name',
    align: 'left',
  },
  { name: 'total_score', label: 'Score', field: 'total_score', align: 'right' },
  {
    name: 'max_possible_score',
    label: 'Max Score',
    field: 'max_possible_score',
    align: 'right',
  },
  {
    name: 'performance_percentage',
    label: '%',
    field: 'performance_percentage',
    align: 'right',
  },
  { name: 'created_at', label: 'Date', field: 'created_at', align: 'right' },
]

const resultsColumns = computed(() => {
  const columns = [
    {
      name: 'property',
      label: 'Property',
      field: 'property',
      align: 'left',
      style: 'width: 30%',
    },
  ]

  // Only show price columns for current user's session
  if (props.isCurrentUserSession) {
    columns.push(
      {
        name: 'guess',
        label: 'Your Guess',
        field: 'guess',
        align: 'right',
        style: 'width: 20%',
      },
      {
        name: 'actual',
        label: 'Actual Price',
        field: 'actual',
        align: 'right',
        style: 'width: 20%',
      }
    )
  }

  // Always show difference and score
  columns.push(
    {
      name: 'difference',
      label: 'Difference',
      field: 'difference',
      align: 'center',
      style: props.isCurrentUserSession ? 'width: 15%' : 'width: 35%',
    },
    {
      name: 'score',
      label: 'Score',
      field: 'score',
      align: 'center',
      style: props.isCurrentUserSession ? 'width: 15%' : 'width: 35%',
    }
  )

  return columns
})

const isCurrentUserSessionOrUuidRoute = computed(() => {
  return (
    route.name === 'rPriceGameResultsDetailedByUuid' ||
    props.isCurrentUserSession
  )
})

// Helper functions for ResultsTable component
const formatGuessPrice = (row) => {
  if (!props.isCurrentUserSession) return ''

  return props.formatPriceWithBothCurrencies(
    row.guessed_price_in_ui_currency_cents,
    row.ui_currency,
    true,
    row.source_listing_currency
  )
}

const formatActualPrice = (row) => {
  if (!props.isCurrentUserSession) return ''

  return props.formatPriceWithBothCurrencies(
    row.price_at_time_of_estimate_cents,
    row.source_listing_currency,
    false
  )
}
</script>

<style scoped>
.results-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.performance-badge {
  display: inline-block;
  padding: 2rem;
  border-radius: 16px;
  border: 2px solid #e9ecef;
}

.session-info {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.results-actions {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .results-header {
    padding: 1rem;
  }

  .session-info {
    flex-direction: column;
    align-items: center;
  }
}
</style>
