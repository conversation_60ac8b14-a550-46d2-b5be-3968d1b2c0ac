<template>
  <q-card class="player-info-card q-mb-xl" flat bordered>
    <q-card-section class="q-pa-lg">
      <div class="text-h6 text-weight-medium q-mb-md">
        <q-icon name="person" color="primary" class="q-mr-sm" />
        Player Information
      </div>
      <div class="row q-col-gutter-md">
        <div class="col-12 col-md-6">
          <q-input
            :model-value="playerName"
            @update:model-value="$emit('update:playerName', $event)"
            label="Your name / nickname"
            outlined
            dense
            placeholder="Enter your name or leave blank"
            hint="This helps us show you how you compare to other players"
          >
            <template v-slot:prepend>
              <q-icon name="person" />
            </template>
          </q-input>
        </div>
        <div class="col-12 col-md-6 currency-selector">
          <q-select
            :model-value="selectedCurrency"
            @update:model-value="$emit('update:selectedCurrency', $event)"
            :options="currencyOptions"
            option-label="label"
            option-value="code"
            emit-value
            map-options
            outlined
            dense
            label="Currency"
            hint="Choose your preferred currency for prices"
          >
            <template v-slot:prepend>
              <q-icon name="currency_exchange" />
            </template>
            <template v-slot:option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section avatar>
                  <span class="currency-symbol">{{
                    scope.opt.symbol
                  }}</span>
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ scope.opt.name }}</q-item-label>
                  <q-item-label caption>{{
                    scope.opt.code
                  }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
            <template v-slot:selected>
              <span class="selected-currency" v-if="selectedCurrencyData">
                {{ selectedCurrencyData.symbol }}
                {{ selectedCurrencyData.name }}
              </span>
            </template>
          </q-select>
        </div>
      </div>
      <div class="text-caption text-grey-6 q-mt-md">
        Your guesses will be saved and compared with other players. No
        personal information is required.
      </div>
    </q-card-section>
  </q-card>
</template>

<script>
export default {
  name: 'PlayerInfoCollectionCard',
  props: {
    playerName: {
      type: String,
      default: '',
    },
    selectedCurrency: {
      type: String,
      default: '',
    },
    selectedCurrencyData: {
      type: Object,
      default: () => ({}),
    },
    currencyOptions: {
      type: Array,
      default: () => [],
    },
  },
  emits: ['update:playerName', 'update:selectedCurrency'],
}
</script>

<style scoped>
.player-info-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.currency-symbol {
  font-weight: bold;
  font-size: 1.1rem;
  min-width: 30px;
  text-align: center;
}

.selected-currency {
  font-weight: 500;
}
</style>
