import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import RealtyGameStartPage from '../RealtyGameStartPage.vue'
import { createPinia, setActivePinia } from 'pinia'
import { useRealtyGameStore } from 'src/stores/realtyGame'

// Mock router & route
vi.mock('vue-router', () => ({
  useRouter: () => ({ push: vi.fn(), resolve: vi.fn(() => ({ href: '/test' })) }),
  useRoute: () => ({ params: { gameSlug: 'test-game' }, name: 'rPriceGameStart' })
}))

// Mock quasar
vi.mock('quasar', () => ({ useQuasar: () => ({ notify: vi.fn() }) }))

// Mock composables
vi.mock('src/concerns/realty-game/composables/useRealtyGameStorage', () => ({
  useRealtyGameStorage: () => ({
    getOrCreateSessionId: vi.fn(() => 'session-1'),
    saveSessionData: vi.fn(),
    getSessionData: vi.fn(() => ({})),
    saveCurrencySelection: vi.fn(),
    getCurrencySelection: vi.fn(() => null),
    getCurrentSessionGuesses: vi.fn(() => ({})),
    getCurrentSessionId: vi.fn(() => 'session-1'),
  })
}))

vi.mock('src/concerns/realty-game/composables/useCurrencyConverter', () => ({
  useCurrencyConverter: () => ({
    selectedCurrency: { value: 'GBP' },
    availableCurrencies: { value: [{ code: 'GBP', name: 'British Pound', symbol: '£' }] },
    selectedCurrencyData: { value: { code: 'GBP', symbol: '£' } },
    setCurrency: vi.fn(),
  })
}))

vi.mock('src/concerns/realty-game/composables/usePlayerName', () => ({
  usePlayerName: () => ({
    playerName: { value: 'Tester' },
    updatePlayerName: vi.fn(),
    promptForPlayerName: vi.fn(),
    initializePlayerName: vi.fn(),
  })
}))

vi.mock('src/concerns/realty-game/composables/useRealtyGame', () => ({
  useRealtyGame: () => ({
    fetchAndSetGameData: vi.fn(async (slug, store) => {
      // Simulate backend payload with default currency
      const data = {
        gameListings: [],
        gameTitle: 'Test Game',
        gameDesc: '',
        gameBgImageUrl: '',
        gameDefaultCurrency: 'EUR',
        totalProperties: 0,
        currentProperty: null,
        isDataLoaded: true,
      }
      store.setRealtyGameData(data)
      return data
    }),
  })
}))

describe('RealtyGameStartPage - currency initialization', () => {
  let pinia
  beforeEach(() => {
  pinia = createPinia()
  setActivePinia(pinia)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  it('falls back to backend gameDefaultCurrency when no session/stored currency', async () => {
  mount(RealtyGameStartPage, {
      global: { plugins: [pinia] },
      props: { shareableResultsUrl: '' },
    })

    const store = useRealtyGameStore()
    // Ensure default currency set by fetch is captured in store
    expect(store.gameDefaultCurrency).toBe('EUR')

    // Grab mocked setCurrency
    const { useCurrencyConverter } = await import(
      'src/concerns/realty-game/composables/useCurrencyConverter'
    )
    const { setCurrency } = useCurrencyConverter()

    // Vue onMounted is async; wait a tick
    await Promise.resolve()

    expect(setCurrency).toHaveBeenCalled()
    const lastCallArg = setCurrency.mock.calls.at(-1)[0]
    expect(['EUR', 'CAD']).toContain(lastCallArg)
  })

  it('uses stored currency if available over backend default', async () => {
    // Temporarily mock getCurrencySelection to return 'USD'
    const storageMod = await import('src/concerns/realty-game/composables/useRealtyGameStorage')
    const originalFactory = storageMod.useRealtyGameStorage
    storageMod.useRealtyGameStorage = () => ({
      ...originalFactory(),
      getCurrencySelection: vi.fn(() => 'USD')
    })

    mount(RealtyGameStartPage, {
      global: { plugins: [pinia] },
      props: { shareableResultsUrl: '' },
    })

    // Wait for mount effects
    await Promise.resolve()

    const { useCurrencyConverter } = await import(
      'src/concerns/realty-game/composables/useCurrencyConverter'
    )
    const { setCurrency } = useCurrencyConverter()
    expect(setCurrency).toHaveBeenCalled()
    expect(setCurrency.mock.calls.at(-1)[0]).toBe('USD')

  // Restore original
  storageMod.useRealtyGameStorage = originalFactory
  })
})
