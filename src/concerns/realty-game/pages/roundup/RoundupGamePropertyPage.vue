<template>
  <div class="realty-game-property-page" :style="backgroundStyle">
    <!-- JSON-LD for Property and Game -->
    <PropertyJsonLd
      v-if="currentProperty"
      :property="currentProperty"
      :breadcrumbs="breadcrumbs"
    />

    <q-no-ssr>
      <div class="max-ctr q-pa-xs rupgpp">
        <!-- Loading State -->
        <div
          v-if="isLoading || isFetchingProperty"
          class="loading-container text-center q-pa-xl"
        >
          <q-spinner color="primary" size="3em" />
          <div class="q-mt-md text-h6">Loading Property...</div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="error-container text-center q-pa-xl">
          <q-icon name="error" color="negative" size="3em" />
          <div class="q-mt-md text-h6 text-negative">
            Error Loading Property
          </div>
          <div class="text-body2 text-grey-7 q-mb-lg">{{ error }}</div>
          <q-btn color="primary" label="Go Back" @click="$router.go(-1)" />
        </div>

        <!-- Property Content -->
        <div v-else-if="currentProperty" class="property-content">
          <!-- <div class="rug-prop-header q-mb-lg">
            <div class="row items-center justify-between">
              <div class="header-info col-auto q-pa-md">
                <h1 class="text-h4 text-weight-bold text-primary q-my-xs">
                  {{ currentProperty.title }}
                </h1>
                <p
                  class="text-body1 text-grey-7"
                  v-if="currentProperty.street_address"
                >
                  {{ currentProperty.street_address }}
                </p>
                <p class="text-body1 text-grey-7" v-else>
                  {{ currentProperty.city }},
                  {{ currentProperty.postal_code || currentProperty.region }}
                </p>
                <div class="text-caption text-grey-6 q-mt-xs" v-if="gameTitle">
                  Part of {{ gameTitle }}
                </div>
              </div>

              <div class="header-thumbnail col-auto mobile-hide">
                <div class="thumbnail-container" v-if="propertyImages.length > 0">
                  <img
                    :src="propertyImages[0].image_details.url"
                    :alt="currentProperty.title"
                    class="property-thumbnail"
                  />
                  <div class="thumbnail-overlay">
                   
                  </div>
                </div>
                <div class="thumbnail-placeholder" v-else>
                  <q-icon name="home" size="lg" color="grey-5" />
                </div>
              </div>
            </div>
          </div> -->

          <!-- Property Display -->
          <div class="row q-col-gutter-lg rgpp-main q-mt-md">
            <!-- Property Images and Details -->
            <div class="col-12 col-lg-8">
              <q-card class="property-card" flat bordered>
                <!-- Image Carousel -->
                <div class="m-guess-property-carousel" style="height: 100%">
                  <q-carousel
                    v-model="currentImageIndex"
                    animated
                    swipeable
                    thumbnails
                    arrows
                    infinite
                    height="400px"
                    class="rounded-borders"
                    control-color="primary"
                    control-type="flat"
                  >
                    <q-carousel-slide
                      v-for="(image, index) in propertyImages"
                      :key="index"
                      :name="index"
                      :img-src="image.image_details.url"
                      class="carousel-slide"
                    >
                      <!-- Uncomment if you want to restore captions -->
                      <!-- <div class="absolute-bottom custom-caption">
        <div class="text-subtitle2">{{ image.photo_title || `Photo ${index + 1}` }}</div>
      </div> -->
                    </q-carousel-slide>

                    <!-- Custom navigation buttons slot -->
                    <template v-slot:control>
                      <q-btn
                        class="carousel-nav-btn prev-btn"
                        icon="chevron_left"
                        round
                        unelevated
                        color="white"
                        text-color="primary"
                        size="sm"
                        @click="
                          currentImageIndex =
                            (currentImageIndex - 1 + propertyImages.length) %
                            propertyImages.length
                        "
                      />
                      <q-btn
                        class="carousel-nav-btn next-btn"
                        icon="chevron_right"
                        round
                        unelevated
                        color="white"
                        text-color="primary"
                        size="sm"
                        @click="
                          currentImageIndex =
                            (currentImageIndex + 1) % propertyImages.length
                        "
                      />
                    </template>
                  </q-carousel>
                </div>
                <!-- Property Details -->
                <q-card-section class="q-pa-lg">
                  <div class="property-features">
                    <div class="row q-col-gutter-md q-mb-lg">
                      <div
                        class="col-auto"
                        v-if="currentProperty.count_bedrooms"
                      >
                        <div class="feature-item">
                          <q-icon
                            name="bed"
                            color="primary"
                            size="sm"
                            class="q-mr-sm"
                          />
                          <span class="text-weight-medium"
                            >{{ currentProperty.count_bedrooms }} Bedrooms</span
                          >
                        </div>
                      </div>
                      <div
                        class="col-auto"
                        v-if="currentProperty.count_bathrooms"
                      >
                        <div class="feature-item">
                          <q-icon
                            name="bathtub"
                            color="primary"
                            size="sm"
                            class="q-mr-sm"
                          />
                          <span class="text-weight-medium"
                            >{{
                              currentProperty.count_bathrooms
                            }}
                            Bathrooms</span
                          >
                        </div>
                      </div>
                      <div
                        class="col-auto"
                        v-if="currentProperty.count_garages"
                      >
                        <div class="feature-item">
                          <q-icon
                            name="garage"
                            color="primary"
                            size="sm"
                            class="q-mr-sm"
                          />
                          <span class="text-weight-medium"
                            >{{ currentProperty.count_garages }} Garages</span
                          >
                        </div>
                      </div>
                    </div>

                    <div
                      v-if="currentProperty.gl_description_atr || currentProperty.description"
                      class="property-description"
                    >
                      <div class="text-subtitle2 text-weight-medium q-mb-sm">
                        Description
                      </div>
                      <div
                        class="text-body2 text-grey-9"
                        v-html="currentProperty.gl_description_atr || currentProperty.description"
                      ></div>
                      <!-- <p class="text-body2 text-grey-9">
                      {{ currentProperty.gl_description_atr || currentProperty.description }}</p> -->
                    </div>
                  </div>
                  <q-no-ssr>
                    <div
                      class="q-my-lg pgpp-leaflet-map-ctr"
                      v-if="currentProperty && currentProperty.latitude"
                    >
                      <PriceGuessLeafletMap
                        :key="currentProperty?.uuid"
                        :currentProperty="currentProperty"
                      />
                    </div>
                    <div
                      class="q-my-lg pgpp-boundary-map-ctr"
                      v-else-if="
                        currentProperty && currentProperty.listing_geo_json
                      "
                    >
                      <BoundaryLeafletMap
                        :key="currentProperty?.uuid"
                        :currentProperty="currentProperty"
                      >
                      </BoundaryLeafletMap>
                    </div>
                    <!-- <div class="q-my-lg pgpp-embed-map-ctr"
                       v-else-if="currentProperty && currentProperty.city">
                    <PriceGuessGoogleMapEmbed :key="currentProperty?.uuid"
                                              :embedLocation="currentProperty.city"></PriceGuessGoogleMapEmbed>
                  </div> -->
                  </q-no-ssr>
                </q-card-section>
              </q-card>
            </div>

            <!-- Guess Section -->
            <div class="col-12 col-lg-4">
              <q-card class="guess-card" flat bordered>
                <q-card-section class="q-pa-lg">
                  <!-- <div class="text-h6 text-weight-medium q-mb-md">
                  <q-icon name="attach_money"
                          color="primary"
                          class="q-mr-sm" />
                  What's Your Guess?
                </div>

                <div class="text-body2 text-grey-7 q-mb-lg">
                  Based on the photos and details, what do you think this property is worth?
                </div> -->

                  <div class="text-h6 text-weight-medium q-mb-md">
                    <q-icon
                      name="attach_money"
                      color="primary"
                      class="q-mr-sm"
                    />
                    <span v-if="!propertyAlreadyGuessed"
                      >What's Your Guess {{ playerName }}?</span
                    >
                    <span v-else>Your Previous Guess</span>
                  </div>

                  <!-- Already guessed message -->
                  <div
                    v-if="propertyAlreadyGuessed"
                    class="existing-guess-info q-mb-lg"
                  >
                    <q-banner class="bg-positive text-white rounded-borders">
                      <template v-slot:avatar>
                        <q-icon name="check_circle" />
                      </template>
                      <div class="text-body2">
                        Great job! You've already made a guess for this
                        property.
                      </div>
                      <div
                        v-if="existingGuessData"
                        class="text-caption q-mt-xs"
                      >
                        Submitted on
                        {{
                          new Date(
                            existingGuessData.submittedAt
                          ).toLocaleDateString()
                        }}
                        • Score: {{ existingGuessData.score }}/100
                      </div>
                    </q-banner>
                  </div>

                  <div v-else class="text-body2 text-grey-7 q-mb-lg">
                    Take your time! Based on the photos and details, what do you
                    think this property is worth?
                  </div>

                  <!-- Human-Friendly Price Display -->
                  <div
                    v-if="userGuess && userGuess > 0"
                    class="text-body2 text-grey-7 q-mb-sm"
                  >
                    Your guess:
                    {{
                      formatPriceWithBothCurrencies(
                        (userGuess || 0) * 100,
                        selectedCurrency,
                        true,
                        currentProperty?.currency
                      )
                    }}
                  </div>
                  <!-- Price Input -->
                  <q-input
                    v-model.number="userGuess"
                    type="number"
                    label="Your price estimate"
                    outlined
                    dense
                    :prefix="currencyPrefix"
                    placeholder="Enter your guess"
                    class="q-mb-md"
                    :error="validationErrors.length > 0"
                    :error-message="validationErrors.join(', ')"
                    @keyup.enter="handleSubmitRupGameGuess"
                  >
                    <template v-slot:append>
                      <q-icon name="help_outline" color="grey-5">
                        <q-tooltip
                          >Enter the amount you think this property is
                          worth</q-tooltip
                        >
                      </q-icon>
                    </template>
                  </q-input>

                  <!-- Existing guess details -->
                  <div
                    v-if="propertyAlreadyGuessed && existingGuessData"
                    class="existing-guess-details q-mb-md"
                  >
                    <q-card flat bordered class="bg-grey-1">
                      <q-card-section class="q-pa-md">
                        <!-- <div class="text-subtitle2 text-weight-medium q-mb-sm">Your Result:</div> -->
                        <div>
                          <!-- <div class="result-row q-mb-md">
                            <div class="result-label">Your Guess:</div>
                            <div class="result-value text-weight-bold">
                              {{ formatPriceWithBothCurrencies((userGuess || 0) * 100, selectedCurrency,
                                true, currentProperty?.currency) }}
                            </div>
                          </div> -->

                          <div class="result-row q-mb-md">
                            <div class="result-label">Actual Price:</div>
                            <div class="result-value text-weight-bold">
                              {{
                                formatPriceWithBothCurrencies(
                                  currentProperty?.price_sale_current_cents,
                                  currentProperty?.currency,
                                  false
                                )
                              }}
                            </div>
                          </div>
                        </div>
                        <div class="row q-col-gutter-sm">
                          <div class="col-6">
                            <div class="text-caption text-grey-6">Score</div>
                            <div
                              class="text-h6 text-weight-bold"
                              :class="`text-${getScoreColor(
                                existingGuessData.score
                              )}`"
                            >
                              {{ existingGuessData.score }}/100
                            </div>
                          </div>
                          <div class="col-6">
                            <div class="text-caption text-grey-6">
                              Difference
                            </div>
                            <div
                              class="text-body1 text-weight-medium"
                              :class="
                                existingGuessData.difference > 0
                                  ? 'text-negative'
                                  : 'text-positive'
                              "
                            >
                              {{ existingGuessData.difference > 0 ? '+' : ''
                              }}{{ existingGuessData.difference?.toFixed(1) }}%
                            </div>
                          </div>
                        </div>
                      </q-card-section>
                    </q-card>
                  </div>

                  <!-- Submit Button -->
                  <q-btn
                    v-if="!propertyAlreadyGuessed"
                    color="primary"
                    label="Submit Guess"
                    icon="send"
                    unelevated
                    rounded
                    size="lg"
                    class="full-width q-mb-md"
                    :disable="!userGuess || isSubmitting"
                    :loading="isSubmitting"
                    @click="handleSubmitRupGameGuess"
                  />

                  <!-- View Results Button -->
                  <q-btn
                    v-else
                    :label="isLastProperty ? 'View Results' : 'Next Property'"
                    color="secondary"
                    :icon="isLastProperty ? 'assessment' : 'arrow_forward'"
                    unelevated
                    rounded
                    size="lg"
                    class="full-width q-mb-md"
                    @click="handleNextProperty"
                  />

                  <!-- Personalized Hint -->
                  <!-- <div v-if="!propertyAlreadyGuessed"
                       class="hint-section q-mt-md">
                  </div>
                  <div v-else
                       class="text-caption text-grey-6 text-center q-mt-md">
                    Great work! Ready for the next challenge?
                  </div> -->

                  <!-- <q-btn flat
                       :label="isLastProperty ? 'View Results' : 'Next Property'"
                       color="primary"
                       :icon="isLastProperty ? 'flag' : 'arrow_forward'"
                       @click="handleNextProperty" /> -->
                </q-card-section>
              </q-card>
            </div>
          </div>
        </div>
      </div>
    </q-no-ssr>

    <!-- Feedback Dialog -->
    <q-dialog
      v-model="showGameResultPopup"
      maximized
      transition-show="slide-up"
      transition-hide="slide-down"
    >
      <q-card class="feedback-card">
        <q-card-section
          class="feedback-header text-white"
          :class="`bg-${getScoreColor(currentResult?.score || 0)}`"
        >
          <div class="row items-center">
            <q-icon
              :name="
                currentResult?.score >= 80
                  ? 'celebration'
                  : currentResult?.score >= 60
                  ? 'thumb_up'
                  : 'info'
              "
              size="md"
              class="q-mr-md"
            />
            <div class="flex-1">
              <div class="text-h6">Guess Result</div>
              <div class="text-subtitle2">
                Score: {{ currentResult?.score || 0 }}/100
              </div>
            </div>
            <q-btn
              icon="close"
              flat
              round
              dense
              color="white"
              @click="handleNextProperty"
              class="q-ml-md"
            />
          </div>
        </q-card-section>

        <q-card-section class="q-pa-lg flex-1 scroll">
          <div class="result-details">
            <div class="result-row q-mb-md">
              <div class="result-label">Your Guess:</div>
              <div class="result-value text-weight-bold">
                {{
                  formatPriceWithBothCurrencies(
                    currentResult?.savedEstimate?.guessed_price
                      ?.guessed_price_in_ui_currency_cents || 0,
                    selectedCurrency,
                    true,
                    currentProperty?.currency
                  )
                }}
                <!-- {{ formatPriceWithBothCurrencies((currentResult?.guess || 0) * 100, currentProperty?.currency, false) }} -->
              </div>
            </div>

            <div class="result-row q-mb-md">
              <div class="result-label">Actual Price:</div>
              <div class="result-value text-weight-bold">
                {{
                  formatPriceWithBothCurrencies(
                    currentProperty?.price_sale_current_cents,
                    currentProperty?.currency,
                    false
                  )
                }}
              </div>
            </div>

            <div class="result-row q-mb-md">
              <div class="result-label">Difference:</div>
              <div
                class="result-value"
                :class="
                  currentResult?.difference > 0
                    ? 'text-negative'
                    : 'text-positive'
                "
              >
                {{ currentResult?.difference > 0 ? '+' : ''
                }}{{ currentResult?.difference?.toFixed(1) || 0 }}%
              </div>
            </div>

            <q-separator class="q-my-md" />

            <div class="feedback-message text-center q-pa-md">
              <div class="text-body1">{{ currentResult?.feedback }}</div>
            </div>

            <!-- Optional Property Feedback Section -->
            <PropertyFeedbackSection
              v-model="showPropertyFeedback"
              :current-property="currentProperty"
              :current-result="currentResult"
              :player-name="playerName"
              :route-sssn-id="routeSssnId || currPlayerSssnId"
              @feedback-submitted="propertyFeedbackSubmitted = true"
            />
          </div>
        </q-card-section>

        <!-- Fixed Action Bar for Mobile -->
        <q-card-actions class="fixed-actions bg-white q-pa-md">
          <div
            class="diag-prog-sect row full-width q-gutter-md"
            style="max-width: 80vw"
          >
            <q-btn
              :label="isLastProperty ? 'View Results' : 'Next Property'"
              color="primary"
              :icon="isLastProperty ? 'flag' : 'arrow_forward'"
              @click="handleNextProperty"
              class="col"
              size="lg"
              unelevated
            />

            <q-btn
              v-if="!showPropertyFeedback && !propertyFeedbackSubmitted"
              label="Add Comment"
              color="grey-6"
              icon="comment"
              @click="showPropertyFeedback = true"
              flat
              class="col-auto"
              size="lg"
            />
          </div>
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
import { useRealtyGame } from 'src/concerns/realty-game/composables/useRealtyGame'
import { useRealtyGameStorage } from 'src/concerns/realty-game/composables/useRealtyGameStorage'
import { useCurrencyConverter } from 'src/concerns/realty-game/composables/useCurrencyConverter'
import { usePlayerName } from 'src/concerns/realty-game/composables/usePlayerName'
import { useRealtyGameStore } from 'src/stores/realtyGame'
import { useRealtyGameMetaStore } from 'src/stores/realtyGameMeta'
import useJsonLd from 'src/compose/useJsonLd.js'
import PriceGuessLeafletMap from 'src/concerns/price-guess/components/PriceGuessLeafletMap.vue'
import BoundaryLeafletMap from 'src/concerns/price-guess/components/BoundaryLeafletMap.vue'
import PropertyJsonLd from 'src/components/seo/PropertyJsonLd.vue'
import PropertyFeedbackSection from 'src/concerns/realty-game/components/PropertyFeedbackSection.vue'

export default {
  name: 'RoundupGamePropertyPage',

  components: {
    PriceGuessLeafletMap,
    BoundaryLeafletMap,
    PropertyJsonLd,
    PropertyFeedbackSection,
  },

  props: {
    listingInGameUuid: {
      type: String,
      required: true,
    },
    routeSssnId: {
      type: String,
      required: false,
    },
    currPlayerSssnId: {
      type: String,
      required: false,
    },
    // Props from layout
    gameCommunitiesDetails: {
      type: Object,
    },
    shareableResultsUrl: {
      type: String,
    },
    isCurrentUserSession: {
      type: Boolean,
      default: true,
    },
    gameTitle: {
      type: String,
    },
    gameDefaultCurrency: {
      type: String,
    },
    totalProperties: {
      type: Number,
    },
    firstPropListing: {
      type: Object,
    },
    realtyGameSummary: {
      type: Object,
    },
    // Property data from layout
    currentPropertyListing: {
      type: Object,
      default: null,
    },
    isPropertyLoading: {
      type: Boolean,
      default: false,
    },
    propertyError: {
      type: [String, null],
      default: null,
    },
  },

  emits: ['update-progress', 'game-complete'],

  // preFetch hook for SSR and data prefetching
  async preFetch({ currentRoute, ssrContext }) {
    console.log(
      'RoundupGamePropertyPage preFetch called! Route:',
      currentRoute.path,
      'Params:',
      currentRoute.params
    )

    const gameSlug = currentRoute.params.gameSlug
    const listingInGameUuid = currentRoute.params.listingInGameUuid

    if (!gameSlug || !listingInGameUuid) {
      console.warn(
        'Missing gameSlug or listingInGameUuid in preFetch. Route:',
        currentRoute.path
      )
      return
    }

    const { default: axios } = await import('axios')
    const { pwbFlexConfig } = await import('boot/pwb-flex-conf')
    const { useRealtyGameStore } = await import('src/stores/realtyGame')

    try {
      console.log(
        'RoundupGamePropertyPage preFetch - fetching data for:',
        'gameSlug:',
        gameSlug,
        'listingInGameUuid:',
        listingInGameUuid
      )

      const [gameResponse, propertyResponse] = await Promise.all([
        axios.get(
          `${pwbFlexConfig.dataApiBase}/api_public/v4/realty_game_summary/${gameSlug}`
        ),
        axios.get(
          `${pwbFlexConfig.dataApiBase}/api_public/v4/game_sale_listings/show_rgl/${listingInGameUuid}`
        ),
      ])

      // Store data in Pinia store for SSR persistence
      if (gameResponse.data) {
        const gameData = gameResponse.data.price_guess_inputs
        const realtyGameDetails = gameResponse.data.realty_game_details
        const gameListings =
          gameData?.game_listings?.filter(
            (game) => game.listing_details.visible === true
          ) || []

        // Update Pinia store state
        const store = useRealtyGameStore()

        // Transform the nested structure to flattened structure for the store
        // The store expects properties with uuid directly, not nested in listing_details
        const flattenedGameListings = gameListings.map((gListingItem) => ({
          // Use the parent object's UUID if it exists, otherwise use listing_details UUID
          uuid: gListingItem.uuid,
          // Flatten all listing_details properties to the top level
          ...gListingItem.listing_details,
          // Keep the original parent UUID for reference if needed
          parentGameListingUuid: gListingItem.uuid,
          listing_details_uuid: gListingItem.listing_details.uuid,
          // Preserve parent object properties like gl_title_atr and gl_description_atr
          gl_title_atr: gListingItem.gl_title_atr,
          gl_description_atr: gListingItem.gl_description_atr,
        }))

        const storeData = {
          gameListings: flattenedGameListings,
          gameTitle:
            realtyGameDetails?.game_title || 'Property Price Challenge',
          gameDesc: realtyGameDetails?.game_description || '',
          gameBgImageUrl: realtyGameDetails?.game_bg_image_url || '',
          gameDefaultCurrency: gameData?.default_currency || 'GBP',
          totalProperties: gameListings.length,
          isDataLoaded: true,
        }

        // Add property data if available
        if (propertyResponse?.data) {
          // Find the corresponding game listing to get gl_title_atr and gl_description_atr
          const matchingGameListing = flattenedGameListings.find(
            (listing) =>
              listing.uuid === listingInGameUuid ||
              listing.listing_details_uuid === propertyResponse.data.sale_listing.uuid
          )

          // Enhance property data with game listing data
          const enhancedPropertyData = {
            ...propertyResponse.data.sale_listing,
            gl_title_atr: matchingGameListing?.gl_title_atr,
            gl_description_atr: matchingGameListing?.gl_description_atr,
          }

          storeData.currentProperty = enhancedPropertyData
          storeData.listingInGameUuid = listingInGameUuid
        }

        store.setRealtyGameData(storeData)

        return {
          gameData: gameResponse.data,
          propertyData: propertyResponse?.data || null,
        }
      }
    } catch (error) {
      console.error('RoundupGamePropertyPage preFetch error:', error)
      return null
    }
  },

  setup(props, { emit }) {
    const $route = useRoute()
    const $router = useRouter()
    const $q = useQuasar()

    // Initialize Pinia stores
    const realtyGameStore = useRealtyGameStore()

    // Initialize the price guess composable
    const {
      isLoading,
      error,
      gameListings,
      gameDesc,
      gameBgImageUrl,
      getPropertyByIndex,
      getPropertyByUuid,
      getPropertyImages,
      validateGuess,
      submitGameGuess,
      getScoreColor,
      fetchPriceGuessData,
      fetchPropertyByUuid,
      setRealtyGameData,
    } = useRealtyGame()

    // Initialize the storage composable
    const {
      getOrCreateSessionId,
      // saveSessionData,
      // saveCurrencySelection,
      getSessionData,
      saveGuess,
      getGuess,
      hasGuessed,
      getCurrencySelection,
    } = useRealtyGameStorage()

    // Initialize the currency converter
    const {
      selectedCurrency,
      setCurrency,
      formatPriceWithBothCurrencies,
      getCurrencySymbol,
      convertPrice,
    } = useCurrencyConverter()

    // Initialize player name management
    const {
      playerName,
      isPlayerNameValid,
      ensurePlayerName,
      initializePlayerName,
    } = usePlayerName()

    // Local reactive state
    const userGuess = ref(null)
    const validationErrors = ref([])
    const showGameResultPopup = ref(false)
    const currentResult = ref(null)
    const currentImageIndex = ref(0)
    const isSubmitting = ref(false)
    const fetchedProperty = ref(null) // New state for fetched property data
    const isFetchingProperty = ref(false) // Loading state for property fetch

    // Property feedback state
    const showPropertyFeedback = ref(false)
    const propertyFeedbackText = ref('')
    const propertyFeedbackLoading = ref(false)
    const propertyFeedbackSubmitted = ref(false)

    // Computed properties
    const currentProperty = computed(() => {
      // Priority: 1. Props from layout, 2. Store data, 3. Fetched data, 4. Fallback
      if (props.currentPropertyListing) {
        // New structure: combine realty_game_listing and sale_listing for backward compatibility
        const gameData = props.currentPropertyListing.realty_game_listing || {}
        const saleData = props.currentPropertyListing.sale_listing || {}
        return {
          // Merge both objects for direct property access
          ...gameData,
          ...saleData,
          // Keep nested structure available too
          realty_game_listing: gameData,
          sale_listing: saleData
        }
      }
      if (realtyGameStore.currentProperty) {
        return realtyGameStore.currentProperty
      }
      if (fetchedProperty.value) {
        return fetchedProperty.value
      }
      return getPropertyByUuid(props.listingInGameUuid) || {}
    })

    // const isCurrentProperty = (propUuid) => {
    //   return propUuid === props.listingInGameUuid
    // }

    const propertyImages = computed(() => {
      let allImages = currentProperty.value
        ? getPropertyImages(currentProperty.value)
        : []
      return allImages.filter((image) => !image.flag_is_hidden)
    })

    // filteredImages() {
    // return this.propertyImages.filter(image => !image.flag_is_hidden);
    // },

    const currentPropertyIndex = computed(() => {
      // Find the index of the current property in the properties list
      // Handle both data structures: composable (with listing_details) and store (direct properties)
      const index = gameListings.value.findIndex((prop) => {
        const propUuid = prop.listing_details?.uuid || prop.uuid
        return propUuid === props.listingInGameUuid
      })
      return index >= 0 ? index : 0
    })

    const isLastProperty = computed(() => {
      return true
    })

    // Initialize currency from session
    const sessionCurrency = getCurrencySelection(props.routeSssnId)
    if (sessionCurrency) {
      setCurrency(sessionCurrency)
    }

    const currencyPrefix = computed(() => {
      return getCurrencySymbol()
    })

    // Check if this property has already been guessed
    const propertyAlreadyGuessed = computed(() => {
      if (!currentProperty.value) return false
      return hasGuessed(currentProperty.value.uuid, props.routeSssnId)
    })

    // Get existing guess data
    const existingGuessData = computed(() => {
      if (!currentProperty.value) return null
      return getGuess(currentProperty.value.uuid, props.routeSssnId)
    })

    // Background style with game background image
    const backgroundStyle = computed(() => {
      return {}
      // if (!gameBgImageUrl.value) return {}

      // return {
      //   backgroundImage: `linear-gradient(rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.95)), url(${gameBgImageUrl.value})`,
      //   backgroundSize: 'cover',
      //   backgroundPosition: 'center',
      //   backgroundAttachment: 'fixed',
      //   minHeight: '100vh'
      // }
    })

    // Breadcrumbs for JSON-LD
    const breadcrumbs = computed(() => {
      if (!currentProperty.value) return []

      return [
        { name: 'Home', url: '/' },
        {
          name: props.gameTitle || 'Property Price Challenge',
          url: '/price-game',
        },
        {
          name: currentProperty.value.title || 'Property Game',
          url: window.location.href,
        },
      ]
    })

    // Initialize JSON-LD composable
    const { addJsonLd, removeJsonLd } = useJsonLd()

    // Methods
    const validateCurrentGuess = () => {
      // Always clear previous errors first
      validationErrors.value = []

      if (!currentProperty.value) return false

      let userGuessInListingCurrency = convertPrice(
        userGuess.value,
        selectedCurrency.value,
        currentProperty.value.currency
      )

      const actualPrice = currentProperty.value.price_sale_current_cents / 100
      const validation = validateGuess(userGuessInListingCurrency, actualPrice)

      if (!validation.isValid) {
        validationErrors.value = validation.errors
        return false
      }
      return true
    }

    const handleSubmitRupGameGuess = async () => {
      // Check if already guessed
      if (propertyAlreadyGuessed.value) {
        $q.notify({
          color: 'warning',
          message: 'You have already made a guess for this property',
          icon: 'warning',
          position: 'top',
        })
        return
      }

      // Validate only on submit
      const isValid = validateCurrentGuess()
      if (!isValid) {
        return
      }

      // Check if currPlayerSssnId is missing and handle it
      if (!props.currPlayerSssnId) {
        console.warn('currPlayerSssnId is missing, attempting to create session')

        // Show a dialog to inform the user about session creation
        try {
          await $q.dialog({
            title: 'Game Session Required',
            message: 'We need to create a game session to save your progress. This will allow you to track your guesses and see results.',
            ok: {
              label: 'Create Session',
              color: 'primary',
              unelevated: true
            },
            cancel: {
              label: 'Cancel',
              color: 'grey',
              flat: true
            },
            persistent: false
          })
        } catch (error) {
          // User cancelled the dialog
          console.log('User cancelled session creation')
          return
        }

        // Try to get or create a session ID
        const sessionId = getOrCreateSessionId()

        if (!sessionId) {
          // If we still can't get a session ID, show error and don't proceed
          $q.notify({
            color: 'negative',
            message: 'Unable to create game session. Please refresh the page and try again.',
            icon: 'error',
            position: 'top',
            timeout: 5000,
          })
          return
        }

        // Show a success notice
        $q.notify({
          color: 'positive',
          message: 'Game session created successfully! You can now submit your guess.',
          icon: 'check_circle',
          position: 'top',
          timeout: 3000,
        })

        // Use the newly created session ID
        console.log('Using newly created session ID:', sessionId)
      }

      isSubmitting.value = true

      try {
        let realtyGameSlug = $route.params.gameSlug
        let userGuessInUiCurrency = userGuess.value
        let uiCurrency = selectedCurrency.value //|| "USD"
        let userGuessInListingCurrency = convertPrice(
          userGuess.value,
          uiCurrency,
          currentProperty.value.currency
        )

        // Use currPlayerSssnId if available, otherwise get/create one
        const sessionIdToUse = props.currPlayerSssnId || getOrCreateSessionId()

        // Final check - ensure we have a valid session ID before proceeding
        if (!sessionIdToUse) {
          console.error('No valid session ID available for submission')
          $q.notify({
            color: 'negative',
            message: 'Unable to submit guess - no valid game session. Please refresh the page and try again.',
            icon: 'error',
            position: 'top',
            timeout: 5000,
          })
          return
        }

        let userInfo = {
          realtyGameSlug: realtyGameSlug,
          userGuessInUiCurrency: userGuessInUiCurrency,
          uiCurrency: uiCurrency,
          name: playerName.value,
          sessionId: sessionIdToUse,
          userUuid: null,
          // scootUuid: dossierUuid.value
        }
        const result = await submitGameGuess(
          userGuessInListingCurrency,
          userInfo,
          currentProperty.value,
          currentPropertyIndex.value
        )

        if (result.success) {
          // Save to local storage
          const guessData = {
            guess: userGuess.value,
            score: result.result.score,
            difference: result.result.difference,
            feedback: result.result.feedback,
            actualPrice: result.result.actualPrice,
            propertyUuid: currentProperty.value.uuid,
            propertyTitle: currentProperty.value.title,
            currency: currentProperty.value.currency,
            playerName: playerName.value,
            submittedAt: new Date().toISOString(),
            // Use the same session ID that was used for submission
            sessionId: sessionIdToUse,
          }

          saveGuess(currentProperty.value.uuid, guessData)

          // result.result.savedEstimate.guessed_price.uuid
          // result.result.savedEstimate.guessed_price.guessed_price_in_ui_currency_cents
          // currentResult.savedEstimate.guessed_price.guessed_price_in_ui_currency_cents
          currentResult.value = result.result
          showGameResultPopup.value = true

          if (result.saveError) {
            $q.notify({
              color: 'warning',
              message: result.saveError,
              icon: 'warning',
              position: 'top',
            })
          }
          // Reset guess input
          userGuess.value = null
          $q.notify({
            color: getScoreColor(result.result.score),
            message: `Score: ${result.result.score}/100`,
            icon: result.result.score >= 80 ? 'celebration' : 'info',
            position: 'top',
          })
        } else {
          validationErrors.value = result.errors
        }
      } catch (error) {
        $q.notify({
          color: 'negative',
          message: 'Failed to submit guess. Please try again.',
          icon: 'error',
          position: 'top',
        })
      } finally {
        isSubmitting.value = false
      }
    }

    const submitPropertyFeedback = async () => {
      if (!propertyFeedbackText.value.trim()) return

      propertyFeedbackLoading.value = true

      try {
        const feedbackData = {
          // dd: currentResult.savedEstimate.guessed_price.guessed_price_in_ui_currency_cents,
          guessed_price_uuid:
            currentResult.value?.savedEstimate?.guessed_price.uuid,
          property_uuid: currentProperty.value.uuid,
          property_title: currentProperty.value.title,
          game_session_id: props.routeSssnId,
          player_name: playerName.value,
          feedback_text: propertyFeedbackText.value.trim(),
          user_guess: currentResult.value?.guess,
          actual_price: currentProperty.value.price_sale_current_cents / 100,
          score: currentResult.value?.score,
          submitted_at: new Date().toISOString(),
          page_url: window.location.href,
        }

        // Submit to backend API
        const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/forms_ppsq/sp/game_property_feedback`
        await axios.post(apiUrl, {
          property_feedback: feedbackData,
        })

        // Show success message
        $q.notify({
          message: 'Thanks for your feedback!',
          color: 'positive',
          icon: 'check_circle',
          position: 'top',
          timeout: 2000,
        })

        // Mark as submitted and close feedback section
        propertyFeedbackSubmitted.value = true
        showPropertyFeedback.value = false
        propertyFeedbackText.value = ''
      } catch (error) {
        console.error('Failed to submit property feedback:', error)
        $q.notify({
          message: 'Failed to submit feedback. Please try again.',
          color: 'negative',
          icon: 'error',
          position: 'top',
        })
      } finally {
        propertyFeedbackLoading.value = false
      }
    }

    const handleNextProperty = () => {
      showGameResultPopup.value = false

      // Reset feedback state for next property
      showPropertyFeedback.value = false
      propertyFeedbackText.value = ''
      propertyFeedbackSubmitted.value = false

      // Though I set userGuess.value = null after submitting
      // below is needed here for where the user had guessed before
      // and the value was coming from local store
      if (!propertyAlreadyGuessed.value) {
        userGuess.value = null
      }

      // Use currPlayerSssnId if available, otherwise get/create one
      const sessionIdForNavigation = props.currPlayerSssnId || getOrCreateSessionId()

      $router
        .push({
          name: 'rRoundupGameResultsSummary',
          params: {
            listingInGameUuid: $route.params.listingInGameUuid,
            gameSlug: $route.params.gameSlug,
            routeSssnId: sessionIdForNavigation,
          },
        })
        .then(() => {
          // window.location.reload()
        })
    }

    // Watch for property changes to emit progress
    watch(
      [currentPropertyIndex, () => props.totalProperties],
      ([newIndex, total]) => {
        if (total > 0) {
          emit('update-progress', {
            currentIndex: newIndex,
            total: total,
          })
        }
      },
      { immediate: true }
    )

    // Initialize on mount
    onMounted(async () => {
      // Initialize player name from storage
      initializePlayerName()

      // Ensure player has a valid name before proceeding
      try {
        await ensurePlayerName({
          title: 'Choose Your Player Name',
          // message: `<p>We've generated this name for you: <strong>{randomName}</strong></p><p>You can use this or create your own.</p>`,
          okLabel: "Let's Go!",
          allowCancel: false,
        })
      } catch (error) {
        console.warn('Player name validation failed:', error)
        // Continue with Anonymous Player if needed
      }

      // Initialize game data (but not property data since watch handles that)
      if (
        realtyGameStore.isDataLoaded &&
        realtyGameStore.gameListings.length > 0
      ) {
        console.log('Using preFetched data from store')
        // Sync store data with composable
        setRealtyGameData({
          properties: realtyGameStore.gameListings,
          gameTitle: realtyGameStore.gameTitle,
          gameDesc: realtyGameStore.gameDesc,
          gameBgImageUrl: realtyGameStore.gameBgImageUrl,
        })
      } else if (!gameListings.value || gameListings.value.length === 0) {
        // Fallback: fetch data if not available from preFetch
        console.log('No preFetched data available, fetching from API')
        try {
          // Try multiple ways to access the gameSlug parameter
          let gameSlug = $route.params.gameSlug

          if (!gameSlug) {
            // Try to find gameSlug in matched routes (parent route)
            for (const matched of $route.matched) {
              if (matched.params.gameSlug) {
                gameSlug = matched.params.gameSlug
                break
              }
            }
          }

          console.log('Fetching data with gameSlug:', gameSlug)
          if (gameSlug) {
            await fetchPriceGuessData(gameSlug)
            console.log(
              'Data fetched successfully. Properties:',
              gameListings.value?.length
            )
          } else {
            console.warn(
              'No gameSlug available for data fetching. Route:',
              $route.path,
              'Params:',
              $route.params
            )
          }
        } catch (err) {
          console.error('Failed to load property data:', err)
        }
      }

      // Load existing guess if available
      loadExistingGuess()

      // Reset form state (but preserve existing guess)
      if (!propertyAlreadyGuessed.value) {
        userGuess.value = null
      }
      validationErrors.value = []
      showGameResultPopup.value = false
      currentResult.value = null
      currentImageIndex.value = 0
    })

    // Load existing guess if available
    const loadExistingGuess = () => {
      if (currentProperty.value && propertyAlreadyGuessed.value) {
        const existingData = existingGuessData.value
        if (existingData) {
          userGuess.value = existingData.guess
        }
      }
    }

    // Remove the meta tags watch since it's now handled in preFetch
    watch(
      [currentProperty, () => props.gameTitle, gameDesc],
      ([property, title, desc]) => {
        if (property && title) {
          const gameJsonLd = {
            '@context': 'https://schema.org',
            '@type': 'Game',
            name: title || 'Property Price Challenge',
            description: desc || 'Interactive property price guessing game',
            genre: 'Educational Game',
            gamePlatform: 'Web Browser',
            applicationCategory: 'Game',
            operatingSystem: 'Any',
            offers: {
              '@type': 'Offer',
              price: '0',
              priceCurrency: 'GBP',
              availability: 'https://schema.org/InStock',
            },
            publisher: {
              '@type': 'Organization',
              name: 'PropertySquares',
              url: window?.location?.origin || '',
            },
            gameItem: {
              '@type': 'RealEstateListing',
              name: property.title,
              description:
                property.description || `Property in ${property.city}`,
              address: {
                '@type': 'PostalAddress',
                addressLocality: property.city,
                addressRegion: property.region,
                postalCode: property.postal_code,
                addressCountry: property.country || 'GB',
              },
              image: propertyImages.value?.[0]?.image_details?.url || '',
              numberOfRooms: property.count_bedrooms,
              numberOfBathroomsTotal: property.count_bathrooms,
            },
            potentialAction: {
              '@type': 'PlayAction',
              target: {
                '@type': 'EntryPoint',
                urlTemplate: window?.location?.href,
              },
            },
          }

          // Remove existing game schema and add new one
          removeJsonLd('game-property-schema')
          addJsonLd(gameJsonLd, 'game-property-schema')
        }
      },
      { immediate: true }
    )

    // Return all reactive values and functions for the template
    return {
      // Reactive state
      userGuess,
      validationErrors,
      showGameResultPopup,
      currentResult,
      currentImageIndex,
      isSubmitting,
      showPropertyFeedback,
      propertyFeedbackText,
      propertyFeedbackLoading,
      propertyFeedbackSubmitted,
      fetchedProperty,
      isFetchingProperty,

      // Computed properties
      currentProperty,
      currentPropertyIndex,
      isLastProperty,
      propertyAlreadyGuessed,
      existingGuessData,
      // propertyImages: getPropertyImages(currentProperty.value),
      backgroundStyle,
      breadcrumbs,
      playerName,

      // Composable methods and state
      isLoading,
      error,
      gameListings,
      gameDesc,
      getPropertyByIndex,
      validateGuess,
      submitGameGuess,
      getScoreColor,

      // Storage methods
      saveGuess,
      getGuess,
      hasGuessed,
      getCurrencySelection,

      // Currency methods
      selectedCurrency,
      setCurrency,
      formatPriceWithBothCurrencies,
      getCurrencySymbol,
      convertPrice,

      // Local methods
      submitPropertyFeedback,
      handleSubmitRupGameGuess,
      handleNextProperty,
      currencyPrefix,
      propertyImages,
    }
  },
}
</script>

<style scoped>
.realty-game-property-page {
  /* background-color: #fafafa; */
  min-height: 100vh;
}

.max-ctr {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container,
.error-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.welcome-banner {
  animation: slideInDown 0.6s ease-out;
}

.welcome-card {
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
  border: 1px solid #e3f2fd;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.welcome-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.rug-prop-header {
  background: white;
  border-radius: 12px;
  /* padding: 1.5rem; */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-thumbnail {
  margin-left: 1rem;
}

.thumbnail-container {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.property-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 4px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #ddd;
}

.property-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.m-guess-property-carousel {
  height: 400px;
}

.carousel-slide {
  background-size: cover;
  background-position: center;
}

.custom-caption {
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 1rem;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  background: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.guess-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 120px;
}

.feedback-card {
  border-radius: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.feedback-header {
  padding: 1.5rem;
  flex-shrink: 0;
}

.property-feedback-section {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.fixed-actions {
  position: sticky;
  bottom: 0;
  border-top: 1px solid #e0e0e0;
  flex-shrink: 0;
  z-index: 10;
}

.result-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.result-label {
  font-weight: 500;
  color: #666;
}

.result-value {
  font-size: 1.1rem;
}

/* Animations */
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.property-content {
  animation: fadeIn 0.8s ease-out;
}

/* Enhanced background styling */
.realty-game-property-page {
  position: relative;
  transition: background-image 0.3s ease;
}

.realty-game-property-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  z-index: -1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .welcome-banner {
    margin-bottom: 1rem;
  }

  .welcome-card .q-card-section {
    padding: 1rem;
  }

  .welcome-card .row {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .rug-prop-header .row {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .property-progress {
    width: 100%;
  }

  .property-progress .q-linear-progress {
    width: 100% !important;
  }

  .m-guess-property-carousel {
    height: 300px;
  }

  .guess-card {
    position: static;
    margin-top: 1rem;
  }

  .feedback-card {
    border-radius: 0;
  }

  .feedback-header {
    padding: 1rem;
  }

  .fixed-actions {
    padding: 1rem;
  }

  .fixed-actions .row {
    flex-direction: column;
    gap: 0.5rem;
  }

  .fixed-actions .q-btn {
    width: 100%;
  }
}

/* Carousel styles start */
/* Carousel navigation buttons */
.carousel-nav-btn {
  background: rgba(255, 255, 255, 0.4) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease;
}

/* .carousel-nav-btn:hover {
  transform: scale(1.1);
} */

.prev-btn {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
}

.next-btn {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
}

/* Thumbnail styles */
:deep(.q-carousel__thumbnail) {
  border: 2px solid transparent;
  border-radius: 4px;
  opacity: 0.7;
  transition: all 0.3s ease;
  width: 60px !important;
  height: 40px !important;
  object-fit: cover;
}

:deep(.q-carousel__thumbnail--active),
:deep(.q-carousel__thumbnail:hover) {
  border-color: var(--q-primary);
  opacity: 1;
}

/* Ensure thumbnails are centered and spaced */
:deep(.q-carousel__thumbnails) {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 8px 0;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 0 0 12px 12px;
}

/* Adjust carousel height for mobile */
@media (max-width: 768px) {
  .m-guess-property-carousel {
    height: 300px;
  }

  .carousel-nav-btn {
    size: md;
    /* Slightly smaller buttons on mobile */
  }

  :deep(.q-carousel__thumbnail) {
    width: 50px !important;
    height: 33px !important;
  }
}

/* Carousel styles end */
</style>
