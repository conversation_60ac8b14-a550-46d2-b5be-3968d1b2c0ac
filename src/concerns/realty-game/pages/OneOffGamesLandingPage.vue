<template>
  <q-layout view="hHh lpR fFf">
    <!-- Header -->
    <q-header 
      :elevated="scrolled" 
      :reveal="true" 
      :class="scrolled ? 'bg-white text-dark shadow-2' : 'bg-transparent text-white'"
      class="transition-all duration-300"
    >
      <q-toolbar class="q-px-lg">
        <!-- Logo -->
        <q-toolbar-title class="flex items-center">
          <q-icon name="mdi-home-analytics" size="md" class="q-mr-sm" />
          <span class="text-h6 font-weight-bold">HousePriceGuess</span>
        </q-toolbar-title>

        <!-- Desktop Navigation -->
        <div class="gt-sm flex q-gutter-md">
          <q-btn 
            flat 
            no-caps 
            @click="scrollToSection('problem')"
            class="nav-btn"
          >
            The Problem
          </q-btn>
          <q-btn 
            flat 
            no-caps 
            @click="scrollToSection('solution')"
            class="nav-btn"
          >
            The Solution
          </q-btn>
          <q-btn 
            flat 
            no-caps 
            @click="scrollToSection('features')"
            class="nav-btn"
          >
            Features
          </q-btn>
          <q-btn 
            flat 
            no-caps 
            @click="scrollToSection('testimonials')"
            class="nav-btn"
          >
            Testimonials
          </q-btn>
        </div>

        <!-- Dark Mode Toggle & CTA -->
        <div class="flex items-center q-gutter-sm">
          <q-toggle 
            v-model="darkMode" 
            @update:model-value="toggleDarkMode"
            icon="mdi-weather-night"
            unchecked-icon="mdi-weather-sunny"
          />
          <q-btn 
            color="accent" 
            no-caps 
            @click="scrollToSection('cta')"
            class="cta-btn"
          >
            Get Started Free
          </q-btn>
        </div>
      </q-toolbar>
    </q-header>

    <!-- Page Container -->
    <q-page-container>
      <!-- Hero Section -->
      <q-page class="hero-section">
        <q-parallax 
          :height="600" 
          :speed="0.5"
          src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1973&q=80"
        >
          <div class="absolute-full flex flex-center">
            <div class="text-center q-pa-xl">
              <h1 class="text-h2 text-white font-weight-bold q-mb-md">
                Tired of vague feedback on your house hunt?
              </h1>
              <h4 class="text-h5 text-white q-mb-xl opacity-90">
                Transform your friends' generic comments into the clear insights you actually need.
              </h4>
              <q-btn 
                size="xl" 
                color="accent" 
                no-caps 
                @click="scrollToSection('solution')"
                class="cta-btn pulse-animation"
              >
                End the Feedback Frustration
              </q-btn>
            </div>
          </div>
        </q-parallax>
      </q-page>

      <!-- Agitation Section -->
      <q-page id="problem" class="q-pa-xl bg-grey-1">
        <div class="container">
          <div class="text-center q-mb-xl">
            <h2 class="text-h3 font-weight-bold q-mb-md">The Reality of House Hunting Feedback</h2>
            <p class="text-h6 text-grey-7">Sound familiar?</p>
          </div>
          
          <!-- Bento Grid -->
          <div class="row q-col-gutter-lg">
            <div class="col-12 col-md-3">
              <q-card flat bordered class="text-message-card q-pa-md">
                <div class="text-center">
                  <div class="message-bubble q-pa-sm bg-blue-2 rounded-borders">
                    <span class="text-body1">Looks nice 👍</span>
                  </div>
                </div>
              </q-card>
            </div>
            
            <div class="col-12 col-md-3">
              <q-card flat bordered class="email-card q-pa-md text-center">
                <q-icon name="mdi-email" size="4rem" color="grey-5" />
                <q-badge color="red" floating>5</q-badge>
                <div class="q-mt-sm text-caption text-grey-6">Unread replies</div>
              </q-card>
            </div>
            
            <div class="col-12 col-md-6">
              <q-card flat bordered class="shrug-card q-pa-lg text-center">
                <div class="shrug-animation q-mb-md">🤷‍♂️</div>
                <p class="text-h6 font-weight-medium">
                  "Is their silence a yes or a no? End the feedback frustration."
                </p>
              </q-card>
            </div>
          </div>
        </div>
      </q-page>

      <!-- Solution Section -->
      <q-page id="solution" class="q-pa-xl">
        <div class="container">
          <div class="text-center q-mb-xl">
            <h2 class="text-h3 font-weight-bold q-mb-md">How HousePriceGuess Works</h2>
            <p class="text-h6 text-grey-7">Experience the simplicity</p>
          </div>

          <div class="row q-col-gutter-xl">
            <!-- Left Column - Sticky Text -->
            <div class="col-12 col-md-6">
              <div class="sticky-content">
                <q-stepper
                  v-model="currentStep"
                  vertical
                  color="primary"
                  animated
                  class="solution-stepper"
                >
                  <q-step
                    :name="1"
                    title="Share Any Listing. Instantly."
                    icon="mdi-link"
                    :done="currentStep > 1"
                  >
                    <p class="text-body1">
                      Stop sending messy links. Just paste any property listing into HousePriceGuess to start a new feedback quiz.
                    </p>
                  </q-step>

                  <q-step
                    :name="2"
                    title="Friends Take the Fun Quiz."
                    icon="mdi-gamepad-variant"
                    :done="currentStep > 2"
                  >
                    <p class="text-body1">
                      Your friends get a simple, engaging quiz. First, they guess the price, which encourages them to look at the details.
                    </p>
                  </q-step>

                  <q-step
                    :name="3"
                    title="Get Clear, Organized Feedback."
                    icon="mdi-chart-line"
                    :done="currentStep > 3"
                  >
                    <p class="text-body1">
                      All feedback is centralized and structured. See price guesses at a glance and read specific comments on location, layout, and condition.
                    </p>
                  </q-step>
                </q-stepper>
              </div>
            </div>

            <!-- Right Column - Interactive Demo -->
            <div class="col-12 col-md-6">
              <div class="demo-container">
                <q-card class="demo-card q-pa-lg" :class="demoCardClass">
                  <!-- Step 1 Demo -->
                  <div v-if="currentStep === 1" class="step-demo">
                    <h6 class="q-mb-md">Paste Property Link</h6>
                    <q-input
                      v-model="demoLink"
                      outlined
                      label="Paste property link here..."
                      prefix="🔗"
                      class="demo-input"
                    />
                  </div>

                  <!-- Step 2 Demo -->
                  <div v-if="currentStep === 2" class="step-demo">
                    <h6 class="q-mb-md">What's your guess for the price?</h6>
                    <q-slider
                      v-model="priceGuess"
                      :min="100000"
                      :max="1000000"
                      :step="10000"
                      label
                      label-always
                      :label-value="`£${priceGuess.toLocaleString()}`"
                      color="accent"
                      class="q-mb-md"
                    />
                    <q-btn color="primary" class="full-width">Submit Guess</q-btn>
                  </div>

                  <!-- Step 3 Demo -->
                  <div v-if="currentStep === 3" class="step-demo">
                    <h6 class="q-mb-md">Feedback Summary</h6>
                    <q-table
                      :rows="feedbackRows"
                      :columns="feedbackColumns"
                      row-key="name"
                      flat
                      class="q-mb-md"
                    />
                    <div class="q-gutter-sm">
                      <q-chip icon="mdi-thumb-up" color="positive" text-color="white">
                        Layout
                      </q-chip>
                      <q-chip icon="mdi-thumb-down" color="negative" text-color="white">
                        Location
                      </q-chip>
                      <q-chip icon="mdi-thumb-up" color="positive" text-color="white">
                        Condition
                      </q-chip>
                    </div>
                  </div>
                </q-card>
              </div>
            </div>
          </div>
        </div>
      </q-page>

      <!-- Benefits Section -->
      <q-page id="features" class="q-pa-xl bg-grey-1">
        <div class="container">
          <div class="text-center q-mb-xl">
            <h2 class="text-h3 font-weight-bold q-mb-md">Built for Every House Hunter</h2>
            <p class="text-h6 text-grey-7">Tailored solutions for your unique situation</p>
          </div>

          <div class="row q-col-gutter-lg">
            <!-- First-Time Buyer -->
            <div class="col-12 col-md-4">
              <q-card flat bordered class="benefit-card q-pa-lg text-center full-height">
                <q-icon name="mdi-shield-check" size="4rem" color="primary" class="q-mb-md" />
                <h5 class="text-h5 font-weight-bold q-mb-md">Buy with Confidence</h5>
                <p class="text-body1">
                  As a first-time buyer, you're making a huge decision. HousePriceGuess turns your friends' supportive but vague comments into structured data, so you can feel confident you're not missing something obvious.
                </p>
              </q-card>
            </div>

            <!-- The Relocator -->
            <div class="col-12 col-md-4">
              <q-card flat bordered class="benefit-card q-pa-lg text-center full-height">
                <q-icon name="mdi-map-marker-radius" size="4rem" color="accent" class="q-mb-md" />
                <h5 class="text-h5 font-weight-bold q-mb-md">Gain Crucial Local Insights</h5>
                <p class="text-body1">
                  Relocating to a new city? Leverage your friends' local knowledge. Their price guesses and comments will help you understand neighborhood value and spot overpriced listings from a mile away.
                </p>
              </q-card>
            </div>

            <!-- The Couple -->
            <div class="col-12 col-md-4">
              <q-card flat bordered class="benefit-card q-pa-lg text-center full-height">
                <q-icon name="mdi-account-group" size="4rem" color="secondary" class="q-mb-md" />
                <h5 class="text-h5 font-weight-bold q-mb-md">Organize All the Opinions</h5>
                <p class="text-body1">
                  Stop deciphering conflicting texts and emails. HousePriceGuess centralizes all feedback in one place, helping you and your partner see the consensus and have more productive conversations.
                </p>
              </q-card>
            </div>
          </div>
        </div>
      </q-page>

      <!-- Social Proof Section -->
      <q-page id="testimonials" class="testimonials-section q-pa-xl">
        <div class="container">
          <div class="text-center q-mb-xl">
            <h2 class="text-h3 font-weight-bold text-white q-mb-md">What Our Users Say</h2>
            <p class="text-h6 text-white opacity-90">Real feedback from real house hunters</p>
          </div>

          <q-carousel
            v-model="testimonialSlide"
            animated
            arrows
            navigation
            infinite
            :autoplay="5000"
            class="testimonial-carousel"
          >
            <q-carousel-slide
              v-for="(testimonial, index) in testimonials"
              :key="index"
              :name="index"
              class="column no-wrap flex-center"
            >
              <q-card class="testimonial-card glassmorphism q-pa-xl text-center">
                <q-rating
                  v-model="testimonial.rating"
                  readonly
                  size="2em"
                  color="amber"
                  class="q-mb-md"
                />
                <blockquote class="text-h6 q-mb-md text-white">
                  "{{ testimonial.quote }}"
                </blockquote>
                <div class="text-subtitle1 text-white opacity-80">
                  {{ testimonial.name }}, {{ testimonial.persona }}
                </div>
              </q-card>
            </q-carousel-slide>
          </q-carousel>
        </div>
      </q-page>

      <!-- Final CTA Section -->
      <q-page id="cta" class="cta-section q-pa-xl">
        <div class="container">
          <q-card class="cta-card q-pa-xl text-center">
            <h3 class="text-h3 font-weight-bold q-mb-md">
              From 'I guess' to 'great buy!' – make better decisions with friends.
            </h3>
            <p class="text-h6 text-grey-7 q-mb-xl">
              Join thousands of house hunters who've transformed their feedback process
            </p>

            <div class="row justify-center">
              <div class="col-12 col-md-6">
                <q-input
                  v-model="email"
                  outlined
                  label="Email Address"
                  prefix="✉️"
                  class="q-mb-md"
                  size="lg"
                />
                <q-btn
                  size="xl"
                  color="accent"
                  no-caps
                  class="full-width cta-btn pulse-animation"
                  @click="handleSignup"
                >
                  Get Started for Free
                </q-btn>
              </div>
            </div>
          </q-card>
        </div>
      </q-page>
    </q-page-container>

    <!-- Footer -->
    <q-footer class="bg-dark text-white">
      <q-toolbar>
        <div class="container full-width">
          <div class="row justify-between items-center">
            <div class="col-auto">
              <span>&copy; 2025 HousePriceGuess. All rights reserved.</span>
            </div>
            <div class="col-auto">
              <q-btn flat no-caps>Terms of Service</q-btn>
              <q-btn flat no-caps>Privacy Policy</q-btn>
            </div>
            <div class="col-auto">
              <q-btn flat round icon="mdi-twitter" />
              <q-btn flat round icon="mdi-facebook" />
              <q-btn flat round icon="mdi-linkedin" />
            </div>
          </div>
        </div>
      </q-toolbar>
    </q-footer>
  </q-layout>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useQuasar } from 'quasar'

const $q = useQuasar()

// Reactive data
const scrolled = ref(false)
const darkMode = ref($q.dark.isActive)
const currentStep = ref(1)
const demoLink = ref('')
const priceGuess = ref(350000)
const testimonialSlide = ref(0)
const email = ref('')

// Demo data
const feedbackRows = [
  { name: 'Sarah', guess: '£340,000' },
  { name: 'Mike', guess: '£365,000' },
  { name: 'Emma', guess: '£330,000' }
]

const feedbackColumns = [
  { name: 'name', label: 'Friend', field: 'name', align: 'left' },
  { name: 'guess', label: 'Price Guess', field: 'guess', align: 'right' }
]

// Testimonials data
const testimonials = [
  {
    quote: "Finally got clear feedback on properties! My friends' price guesses helped me realize I was looking at overpriced listings.",
    name: "Sarah J.",
    persona: "First-Time Buyer",
    rating: 5
  },
  {
    quote: "Moving to Manchester was stressful, but HousePriceGuess helped me understand local pricing through my friends' insights.",
    name: "Mike R.",
    persona: "Relocating Professional",
    rating: 5
  },
  {
    quote: "No more endless group chats about properties! Everything is organized and we can actually make decisions together.",
    name: "Emma & Tom",
    persona: "House-Hunting Couple",
    rating: 5
  }
]

// Computed
const demoCardClass = ref('demo-step-1')

// Methods
let ticking = false
const handleScroll = () => {
  if (ticking) return
  ticking = true
  requestAnimationFrame(() => {
    scrolled.value = window.scrollY > 100
    ticking = false
  })
}

const toggleDarkMode = (val) => {
  $q.dark.set(val)
}

const scrollToSection = (sectionId) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

const handleSignup = () => {
  if (email.value) {
    $q.notify({
      type: 'positive',
      message: 'Thanks for signing up! We\'ll be in touch soon.',
      position: 'top'
    })
    email.value = ''
  } else {
    $q.notify({
      type: 'negative',
      message: 'Please enter your email address',
      position: 'top'
    })
  }
}

// Lifecycle
onMounted(() => {
  window.addEventListener('scroll', handleScroll, { passive: true })
  // Auto-advance demo steps
  setInterval(() => {
    currentStep.value = currentStep.value >= 3 ? 1 : currentStep.value + 1
    demoCardClass.value = `demo-step-${currentStep.value}`
  }, 4000)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style lang="scss" scoped>
// Design System Variables
$primary-color: #1976d2;
$accent-color: #ff6b35;
$secondary-color: #26a69a;
$dark-color: #1d1d1d;
$light-color: #f5f5f5;

// Typography
.text-h2 {
  font-size: 3.5rem;
  line-height: 1.2;
  font-weight: 700;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }
}

.text-h3 {
  font-size: 2.5rem;
  line-height: 1.3;
  font-weight: 600;

  @media (max-width: 768px) {
    font-size: 2rem;
  }
}

// Layout Components
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

// Header Styles
.q-header {
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.nav-btn {
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }
}

.cta-btn {
  font-weight: 600;
  border-radius: 25px;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
  }
}

// Hero Section
.hero-section {
  position: relative;
  overflow: hidden;
}

// Animations
.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 107, 53, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 53, 0);
  }
}

// Bento Grid Cards
.text-message-card {
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.message-bubble {
  max-width: 200px;
  border-radius: 18px;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid #bbdefb;
  }
}

.email-card {
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.shrug-card {
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.02);
  }
}

.shrug-animation {
  font-size: 4rem;
  animation: shrug 3s ease-in-out infinite;
}

@keyframes shrug {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(-5deg); }
  75% { transform: rotate(5deg); }
}

// Solution Section
.sticky-content {
  position: sticky;
  top: 100px;
}

.solution-stepper {
  background: transparent;
  box-shadow: none;
}

.demo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 500px;
}

.demo-card {
  min-height: 400px;
  transition: all 0.5s ease;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

  &.demo-step-1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  &.demo-step-2 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
  }

  &.demo-step-3 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
  }
}

.step-demo {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.demo-input {
  .q-field__control {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
  }

  .q-field__native {
    color: white;
  }
}

// Benefits Section
.benefit-card {
  transition: all 0.3s ease;
  border-radius: 15px;

  &:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  }
}

// Testimonials Section
.testimonials-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    animation: float 20s ease-in-out infinite;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.testimonial-carousel {
  max-width: 800px;
  margin: 0 auto;
}

.testimonial-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.02);
    background: rgba(255, 255, 255, 0.15);
  }
}

.glassmorphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// CTA Section
.cta-section {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.cta-card {
  max-width: 800px;
  margin: 0 auto;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  background: white;
}

// Responsive Design
@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }

  .q-pa-xl {
    padding: 2rem 1rem;
  }

  .demo-container {
    min-height: 300px;
  }

  .demo-card {
    min-height: 250px;
  }

  .sticky-content {
    position: static;
  }
}

// Dark Mode Overrides
.body--dark {
  .benefit-card {
    background: #2d2d2d;
    border-color: #404040;
  }

  .cta-card {
    background: #2d2d2d;
  }

  .demo-card {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  }
}
</style>
