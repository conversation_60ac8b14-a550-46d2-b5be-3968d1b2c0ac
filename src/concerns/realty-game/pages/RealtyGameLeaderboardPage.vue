<template>
  <q-page class="q-pa-md">
    <div class="leadrbrd-container">
      <!-- Header Section -->
      <div class="text-center q-mb-xl">
        <h1 class="text-h3 text-weight-bold q-mb-md">
          🏆 Property Price Challenge Leaderboard
        </h1>
        <p class="text-subtitle1 text-grey-7">
          See how you rank against other property market experts!
        </p>
      </div>

      <!-- Filter Controls -->
      <div class="row q-mb-lg justify-center">
        <div class="col-12 col-md-8">
          <q-card flat bordered class="q-pa-md">
            <div class="row q-gutter-md items-center">
              <div class="col-12 col-sm-6 col-md-3">
                <q-select
                  v-model="selectedTimeframe"
                  :options="timeframeOptions"
                  label="Time Period"
                  outlined
                  dense
                  @update:model-value="loadLeaderboard"
                />
              </div>
              <div class="col-12 col-sm-6 col-md-3">
                <q-select
                  v-model="selectedGameType"
                  :options="gameTypeOptions"
                  label="Game Type"
                  outlined
                  dense
                  @update:model-value="loadLeaderboard"
                />
              </div>
              <div class="col-12 col-sm-6 col-md-3">
                <q-select
                  v-model="selectedRegion"
                  :options="regionOptions"
                  label="Region"
                  outlined
                  dense
                  @update:model-value="loadLeaderboard"
                />
              </div>
              <div class="col-12 col-sm-6 col-md-3">
                <q-btn
                  color="primary"
                  icon="refresh"
                  label="Refresh"
                  @click="loadLeaderboard"
                  :loading="loading"
                />
              </div>
            </div>
          </q-card>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="text-center q-py-xl">
        <q-spinner-dots size="50px" color="primary" />
        <p class="q-mt-md">Loading leaderboard...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center q-py-xl">
        <q-icon name="error" size="50px" color="negative" />
        <p class="q-mt-md text-negative">{{ error }}</p>
        <q-btn color="primary" @click="loadLeaderboard" label="Try Again" />
      </div>

      <!-- Leaderboard Content -->
      <div v-else-if="leaderboardData.length > 0">
        <!-- Top 3 Podium -->
        <div class="podium-section q-mb-xl" v-if="topThree.length > 0">
          <div class="row justify-center q-gutter-md">
            <!-- Second Place -->
            <div v-if="topThree[1]" class="col-12 col-sm-4 col-md-3">
              <q-card class="podium-card second-place text-center">
                <q-card-section class="q-pb-none">
                  <div class="podium-rank">🥈</div>
                  <q-avatar size="80px" class="q-mb-md">
                    <img :src="topThree[1].avatar || '/icons/favicon-96x96.png'" />
                  </q-avatar>
                  <h5 class="q-ma-none">{{ topThree[1].playerName }}</h5>
                  <p class="text-weight-bold text-h6 q-ma-sm">
                    {{ topThree[1].score }}%
                  </p>
                  <p class="text-caption">{{ topThree[1].gamesPlayed }} games</p>
                </q-card-section>
              </q-card>
            </div>

            <!-- First Place -->
            <div v-if="topThree[0]" class="col-12 col-sm-4 col-md-3">
              <q-card class="podium-card first-place text-center">
                <q-card-section class="q-pb-none">
                  <div class="podium-rank">👑</div>
                  <q-avatar size="100px" class="q-mb-md">
                    <img :src="topThree[0].avatar || '/icons/favicon-96x96.png'" />
                  </q-avatar>
                  <h4 class="q-ma-none text-weight-bold">{{ topThree[0].playerName }}</h4>
                  <p class="text-weight-bold text-h5 q-ma-sm text-primary">
                    {{ topThree[0].score }}%
                  </p>
                  <p class="text-caption">{{ topThree[0].gamesPlayed }} games</p>
                </q-card-section>
              </q-card>
            </div>

            <!-- Third Place -->
            <div v-if="topThree[2]" class="col-12 col-sm-4 col-md-3">
              <q-card class="podium-card third-place text-center">
                <q-card-section class="q-pb-none">
                  <div class="podium-rank">🥉</div>
                  <q-avatar size="80px" class="q-mb-md">
                    <img :src="topThree[2].avatar || '/icons/favicon-96x96.png'" />
                  </q-avatar>
                  <h5 class="q-ma-none">{{ topThree[2].playerName }}</h5>
                  <p class="text-weight-bold text-h6 q-ma-sm">
                    {{ topThree[2].score }}%
                  </p>
                  <p class="text-caption">{{ topThree[2].gamesPlayed }} games</p>
                </q-card-section>
              </q-card>
            </div>
          </div>
        </div>

        <!-- Full Leaderboard Table -->
        <q-card flat bordered>
          <q-card-section>
            <h5 class="q-ma-none q-mb-md">Complete Rankings</h5>
            <q-table
              :rows="leaderboardData"
              :columns="columns"
              row-key="id"
              :pagination="{ rowsPerPage: 20 }"
              flat
              bordered
              class="leadrbrd-table"
            >
              <template v-slot:body-cell-rank="props">
                <q-td :props="props">
                  <div class="rank-cell">
                    <span v-if="props.row.rank <= 3" class="rank-medal">
                      {{ props.row.rank === 1 ? '👑' : props.row.rank === 2 ? '🥈' : '🥉' }}
                    </span>
                    <span class="rank-number">{{ props.row.rank }}</span>
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-player="props">
                <q-td :props="props">
                  <div class="player-cell">
                    <q-avatar size="32px" class="q-mr-sm">
                      <img :src="props.row.avatar || '/icons/favicon-96x96.png'" />
                    </q-avatar>
                    <span>{{ props.row.playerName }}</span>
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-score="props">
                <q-td :props="props">
                  <q-badge
                    :color="getScoreColor(props.row.score)"
                    :label="`${props.row.score}%`"
                    class="text-weight-bold"
                  />
                </q-td>
              </template>

              <template v-slot:body-cell-trend="props">
                <q-td :props="props">
                  <q-icon
                    :name="getTrendIcon(props.row.trend)"
                    :color="getTrendColor(props.row.trend)"
                    size="sm"
                  />
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-card>

        <!-- Player Stats Section -->
        <div v-if="currentPlayerStats" class="q-mt-xl">
          <q-card flat bordered>
            <q-card-section>
              <h5 class="q-ma-none q-mb-md">Your Performance</h5>
              <div class="row q-gutter-md">
                <div class="col-12 col-sm-6 col-md-3">
                  <q-card flat class="bg-blue-1">
                    <q-card-section class="text-center">
                      <div class="text-h4 text-weight-bold">{{ currentPlayerStats.rank }}</div>
                      <div class="text-caption">Your Rank</div>
                    </q-card-section>
                  </q-card>
                </div>
                <div class="col-12 col-sm-6 col-md-3">
                  <q-card flat class="bg-green-1">
                    <q-card-section class="text-center">
                      <div class="text-h4 text-weight-bold">{{ currentPlayerStats.score }}%</div>
                      <div class="text-caption">Average Score</div>
                    </q-card-section>
                  </q-card>
                </div>
                <div class="col-12 col-sm-6 col-md-3">
                  <q-card flat class="bg-orange-1">
                    <q-card-section class="text-center">
                      <div class="text-h4 text-weight-bold">{{ currentPlayerStats.gamesPlayed }}</div>
                      <div class="text-caption">Games Played</div>
                    </q-card-section>
                  </q-card>
                </div>
                <div class="col-12 col-sm-6 col-md-3">
                  <q-card flat class="bg-purple-1">
                    <q-card-section class="text-center">
                      <div class="text-h4 text-weight-bold">{{ currentPlayerStats.bestScore }}%</div>
                      <div class="text-caption">Best Score</div>
                    </q-card-section>
                  </q-card>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center q-py-xl">
        <q-icon name="emoji_events" size="100px" color="grey-5" />
        <h5 class="q-mt-md text-grey-7">No leaderboard data available</h5>
        <p class="text-grey-6">Be the first to play and claim the top spot!</p>
        <q-btn
          color="primary"
          :to="{ name: 'rPriceGameStart' }"
          label="Start Playing"
          icon="play_arrow"
        />
      </div>

      <!-- Call to Action -->
      <div class="text-center q-mt-xl" v-if="leaderboardData.length > 0">
        <q-card flat bordered class="bg-primary text-white">
          <q-card-section>
            <h5 class="q-ma-none q-mb-md">Think you can do better?</h5>
            <p class="q-mb-md">Challenge yourself and climb the rankings!</p>
            <q-btn
              color="white"
              text-color="primary"
              :to="{ name: 'rPriceGameStart' }"
              label="Play Now"
              icon="play_arrow"
              size="lg"
            />
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import axios from 'axios'
import { useQuasar } from 'quasar'

const $q = useQuasar()
const route = useRoute()

// Reactive data
const loading = ref(false)
const error = ref(null)
const leaderboardData = ref([])
const currentPlayerStats = ref(null)

// Filter options
const selectedTimeframe = ref('all')
const selectedGameType = ref('all')
const selectedRegion = ref('all')

const timeframeOptions = [
  { label: 'All Time', value: 'all' },
  { label: 'This Week', value: 'week' },
  { label: 'This Month', value: 'month' },
  { label: 'This Year', value: 'year' }
]

const gameTypeOptions = [
  { label: 'All Games', value: 'all' },
  { label: 'Standard', value: 'standard' },
  { label: 'Speed Round', value: 'speed' },
  { label: 'Expert Mode', value: 'expert' }
]

const regionOptions = [
  { label: 'All Regions', value: 'all' },
  { label: 'London', value: 'london' },
  { label: 'Manchester', value: 'manchester' },
  { label: 'Birmingham', value: 'birmingham' },
  { label: 'Edinburgh', value: 'edinburgh' }
]

// Table columns
const columns = [
  {
    name: 'rank',
    label: 'Rank',
    field: 'rank',
    align: 'center',
    sortable: false
  },
  {
    name: 'player',
    label: 'Player',
    field: 'playerName',
    align: 'left',
    sortable: true
  },
  {
    name: 'score',
    label: 'Average Score',
    field: 'score',
    align: 'center',
    sortable: true
  },
  {
    name: 'gamesPlayed',
    label: 'Games',
    field: 'gamesPlayed',
    align: 'center',
    sortable: true
  },
  {
    name: 'bestScore',
    label: 'Best Score',
    field: 'bestScore',
    align: 'center',
    sortable: true
  },
  {
    name: 'trend',
    label: 'Trend',
    field: 'trend',
    align: 'center',
    sortable: false
  }
]

// Computed properties
const topThree = computed(() => {
  return leaderboardData.value.slice(0, 3)
})

// Methods
const loadLeaderboard = async () => {
  loading.value = true
  error.value = null

  try {
    const params = {
      gameSlug: route.params.gameSlug,
      timeframe: selectedTimeframe.value,
      gameType: selectedGameType.value,
      region: selectedRegion.value
    }

    const response = await axios.get('/api/realty-game/leaderboard', { params })
    
    if (response.data && response.data.leaderboard) {
      leaderboardData.value = response.data.leaderboard.map((item, index) => ({
        ...item,
        rank: index + 1,
        id: item.playerId || index
      }))
      currentPlayerStats.value = response.data.currentPlayer || null
    } else {
      // Mock data for development/testing
      leaderboardData.value = generateMockData()
    }
  } catch (err) {
    console.error('Failed to load leaderboard:', err)
    error.value = 'Failed to load leaderboard. Please try again.'
    
    // For development, show mock data instead of error
    if (process.env.NODE_ENV === 'development') {
      leaderboardData.value = generateMockData()
      error.value = null
    }
  } finally {
    loading.value = false
  }
}

const generateMockData = () => {
  const mockPlayers = [
    'PropertyPro', 'MarketMaster', 'EstateExpert', 'ValueVirtuoso', 'RealtyRocket',
    'PricePundit', 'MarketMaven', 'PropertyPirate', 'EstateEagle', 'ValueViper',
    'RealtyRanger', 'PropertyPanda', 'MarketMagic', 'EstateElite', 'ValueVault',
    'RealtyRebel', 'PropertyPharaoh', 'MarketMonarch', 'EstateEmperor', 'ValueViking'
  ]

  return mockPlayers.map((name, index) => ({
    id: index + 1,
    rank: index + 1,
    playerName: name,
    score: Math.floor(Math.random() * 30) + 70, // 70-99%
    gamesPlayed: Math.floor(Math.random() * 50) + 5,
    bestScore: Math.floor(Math.random() * 20) + 80, // 80-99%
    trend: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)],
    avatar: null
  })).sort((a, b) => b.score - a.score)
}

const getScoreColor = (score) => {
  if (score >= 90) return 'green'
  if (score >= 80) return 'orange'
  if (score >= 70) return 'yellow'
  return 'red'
}

const getTrendIcon = (trend) => {
  switch (trend) {
    case 'up': return 'trending_up'
    case 'down': return 'trending_down'
    default: return 'trending_flat'
  }
}

const getTrendColor = (trend) => {
  switch (trend) {
    case 'up': return 'green'
    case 'down': return 'red'
    default: return 'grey'
  }
}

// Lifecycle
onMounted(() => {
  loadLeaderboard()
})
</script>

<style scoped>
.leadrbrd-container {
  max-width: 1200px;
  margin: 0 auto;
}

.podium-section {
  margin-bottom: 2rem;
}

.podium-card {
  transition: transform 0.3s ease;
  position: relative;
}

.podium-card:hover {
  transform: translateY(-5px);
}

.first-place {
  border: 3px solid #ffd700;
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

.second-place {
  border: 3px solid #c0c0c0;
  box-shadow: 0 6px 20px rgba(192, 192, 192, 0.3);
}

.third-place {
  border: 3px solid #cd7f32;
  box-shadow: 0 6px 20px rgba(205, 127, 50, 0.3);
}

.podium-rank {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.rank-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rank-medal {
  font-size: 1.2rem;
}

.rank-number {
  font-weight: bold;
}

.player-cell {
  display: flex;
  align-items: center;
}

.leadrbrd-table {
  border-radius: 8px;
  overflow: hidden;
}

.leadrbrd-table .q-table__top,
.leadrbrd-table .q-table__bottom,
.leadrbrd-table td,
.leadrbrd-table th {
  border-color: #e0e0e0;
}

.leadrbrd-table tbody tr:hover {
  background-color: #f5f5f5;
}

@media (max-width: 768px) {
  .podium-section .col-sm-4 {
    margin-bottom: 1rem;
  }
  
  .podium-rank {
    font-size: 1.5rem;
  }
}
</style>
