import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { useRealtyGame } from '../useRealtyGame'
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

// Mock dependencies
vi.mock('axios')
vi.mock('boot/pwb-flex-conf', () => ({
  pwbFlexConfig: {
    dataApiBase: 'https://api.example.com'
  }
}))

const mockedAxios = vi.mocked(axios)

describe('fetchAndSetGameData', () => {
  let mockStore
  let fetchAndSetGameData

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks()
    
    // Create mock store
    mockStore = {
      setRealtyGameData: vi.fn()
    }

    // Get the function from the composable
    const { fetchAndSetGameData: fn } = useRealtyGame()
    fetchAndSetGameData = fn
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('successful API responses', () => {
    it('should fetch and process game data successfully', async () => {
      // Arrange
      const gameSlug = 'test-game-slug'
      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              {
                listing_details: {
                  visible: true,
                  uuid: 'property-1',
                  title: 'Beautiful House',
                  price_sale_current_cents: 50000000
                }
              },
              {
                listing_details: {
                  visible: false,
                  uuid: 'property-2',
                  title: 'Hidden House'
                }
              },
              {
                listing_details: {
                  visible: true,
                  uuid: 'property-3',
                  title: 'Another House',
                  price_sale_current_cents: 75000000
                }
              }
            ],
            default_currency: 'EUR'
          },
          realty_game_details: {
            game_title: 'London Property Challenge',
            game_description: 'Test your London property knowledge',
            game_bg_image_url: 'https://example.com/bg.jpg'
          }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      const result = await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://api.example.com/api_public/v4/realty_game_summary/test-game-slug'
      )

      expect(mockStore.setRealtyGameData).toHaveBeenCalledWith({
        gameListings: [
          {
            listing_details: {
              visible: true,
              uuid: 'property-1',
              title: 'Beautiful House',
              price_sale_current_cents: 50000000
            }
          },
          {
            listing_details: {
              visible: true,
              uuid: 'property-3',
              title: 'Another House',
              price_sale_current_cents: 75000000
            }
          }
        ],
        gameTitle: 'London Property Challenge',
        gameDesc: 'Test your London property knowledge',
        gameBgImageUrl: 'https://example.com/bg.jpg',
        gameDefaultCurrency: 'EUR',
        totalProperties: 2,
        currentProperty: null,
        isDataLoaded: true
      })

      expect(result).toEqual({
        gameListings: expect.any(Array),
        gameTitle: 'London Property Challenge',
        gameDesc: 'Test your London property knowledge',
        gameBgImageUrl: 'https://example.com/bg.jpg',
        gameDefaultCurrency: 'EUR',
        totalProperties: 2,
        currentProperty: null,
        isDataLoaded: true
      })

      expect(result.gameListings).toHaveLength(2)
      expect(result.gameListings.every(listing => listing.listing_details.visible)).toBe(true)
    })

    it('should use default values when optional fields are missing', async () => {
      // Arrange
      const gameSlug = 'minimal-game'
      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              {
                listing_details: {
                  visible: true,
                  uuid: 'property-1',
                  title: 'Basic House'
                }
              }
            ]
            // No default_currency
          },
          realty_game_details: {
            // No optional fields and no default_game_currency
          }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      const result = await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      expect(mockStore.setRealtyGameData).toHaveBeenCalledWith({
        gameListings: expect.any(Array),
        gameTitle: 'Property Price Challenge', // Default value
        gameDesc: '', // Default value
        gameBgImageUrl: '', // Default value
        gameDefaultCurrency: 'GBP', // Default value
        totalProperties: 1,
        currentProperty: null,
        isDataLoaded: true
      })

      expect(result.gameTitle).toBe('Property Price Challenge')
      expect(result.gameDesc).toBe('')
      expect(result.gameBgImageUrl).toBe('')
      expect(result.gameDefaultCurrency).toBe('GBP')
    })

    it('should prefer realty_game_details.default_game_currency over price_guess_inputs.default_currency', async () => {
      // Arrange
      const gameSlug = 'prefers-details-currency'
      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              { listing_details: { visible: true, uuid: 'p1' } }
            ],
            default_currency: 'USD'
          },
          realty_game_details: {
            game_title: 'Currency Preference Test',
            default_game_currency: 'EUR'
          }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      const result = await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      expect(result.gameDefaultCurrency).toBe('EUR')
      const setArg = mockStore.setRealtyGameData.mock.calls[0][0]
      expect(setArg.gameDefaultCurrency).toBe('EUR')
    })

    it('should use price_guess_inputs.default_currency when default_game_currency is missing', async () => {
      // Arrange
      const gameSlug = 'fallback-to-inputs-currency'
      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              { listing_details: { visible: true, uuid: 'p1' } }
            ],
            default_currency: 'CAD'
          },
          realty_game_details: {
            game_title: 'Fallback Currency Test'
            // No default_game_currency
          }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      const result = await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      expect(result.gameDefaultCurrency).toBe('CAD')
      const setArg = mockStore.setRealtyGameData.mock.calls[0][0]
      expect(setArg.gameDefaultCurrency).toBe('CAD')
    })

    it('should handle empty game listings', async () => {
      // Arrange
      const gameSlug = 'empty-game'
      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: []
          },
          realty_game_details: {
            game_title: 'Empty Game'
          }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      const result = await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      expect(result.gameListings).toEqual([])
      expect(result.totalProperties).toBe(0)
    })

    it('should filter out all hidden properties', async () => {
      // Arrange
      const gameSlug = 'hidden-properties-game'
      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              {
                listing_details: {
                  visible: false,
                  uuid: 'property-1',
                  title: 'Hidden House 1'
                }
              },
              {
                listing_details: {
                  visible: false,
                  uuid: 'property-2',
                  title: 'Hidden House 2'
                }
              }
            ]
          },
          realty_game_details: {
            game_title: 'All Hidden Game'
          }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      const result = await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      expect(result.gameListings).toEqual([])
      expect(result.totalProperties).toBe(0)
    })
  })

  describe('API response edge cases', () => {
    it('should return null when response has no data', async () => {
      // Arrange
      const gameSlug = 'no-data-game'
      const apiResponse = {
        // No data property
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      const result = await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      expect(result).toBeNull()
      expect(mockStore.setRealtyGameData).not.toHaveBeenCalled()
    })

    it('should return null when response.data is null', async () => {
      // Arrange
      const gameSlug = 'null-data-game'
      const apiResponse = {
        data: null
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      const result = await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      expect(result).toBeNull()
      expect(mockStore.setRealtyGameData).not.toHaveBeenCalled()
    })

    it('should handle missing price_guess_inputs gracefully', async () => {
      // Arrange
      const gameSlug = 'missing-inputs-game'
      const apiResponse = {
        data: {
          // No price_guess_inputs
          realty_game_details: {
            game_title: 'Missing Inputs Game'
          }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      const result = await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      expect(result.gameListings).toEqual([])
      expect(result.totalProperties).toBe(0)
      expect(result.gameDefaultCurrency).toBe('GBP')
    })

    it('should handle missing realty_game_details gracefully', async () => {
      // Arrange
      const gameSlug = 'missing-details-game'
      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              {
                listing_details: {
                  visible: true,
                  uuid: 'property-1',
                  title: 'Test Property'
                }
              }
            ]
          }
          // No realty_game_details
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      const result = await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      expect(result.gameTitle).toBe('Property Price Challenge')
      expect(result.gameDesc).toBe('')
      expect(result.gameBgImageUrl).toBe('')
    })
  })

  describe('error handling', () => {
    it('should propagate network errors', async () => {
      // Arrange
      const gameSlug = 'error-game'
      const networkError = new Error('Network error')
      mockedAxios.get.mockRejectedValue(networkError)

      // Act & Assert
      await expect(fetchAndSetGameData(gameSlug, mockStore)).rejects.toThrow('Network error')
      expect(mockStore.setRealtyGameData).not.toHaveBeenCalled()
    })

    it('should propagate 404 errors', async () => {
      // Arrange
      const gameSlug = 'nonexistent-game'
      const notFoundError = new Error('Request failed with status code 404')
      notFoundError.response = { status: 404 }
      mockedAxios.get.mockRejectedValue(notFoundError)

      // Act & Assert
      await expect(fetchAndSetGameData(gameSlug, mockStore)).rejects.toThrow('Request failed with status code 404')
      expect(mockStore.setRealtyGameData).not.toHaveBeenCalled()
    })

    it('should propagate store errors', async () => {
      // Arrange
      const gameSlug = 'store-error-game'
      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: []
          },
          realty_game_details: {
            game_title: 'Store Error Game'
          }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)
      mockStore.setRealtyGameData.mockImplementation(() => {
        throw new Error('Store update failed')
      })

      // Act & Assert
      await expect(fetchAndSetGameData(gameSlug, mockStore)).rejects.toThrow('Store update failed')
    })
  })

  describe('API endpoint construction', () => {
    it('should construct correct API endpoint', async () => {
      // Arrange
      const gameSlug = 'test-slug-123'
      const apiResponse = {
        data: {
          price_guess_inputs: { game_listings: [] },
          realty_game_details: { game_title: 'Test' }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://api.example.com/api_public/v4/realty_game_summary/test-slug-123'
      )
    })

    it('should handle special characters in gameSlug', async () => {
      // Arrange
      const gameSlug = 'game-with-special-chars_123'
      const apiResponse = {
        data: {
          price_guess_inputs: { game_listings: [] },
          realty_game_details: { game_title: 'Special Chars Game' }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://api.example.com/api_public/v4/realty_game_summary/game-with-special-chars_123'
      )
    })
  })

  describe('data filtering logic', () => {
    it('should only include properties with visible=true', async () => {
      // Arrange
      const gameSlug = 'visibility-test'
      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              { listing_details: { visible: true, uuid: 'visible-1', title: 'Visible Property 1' } },
              { listing_details: { visible: false, uuid: 'hidden-1', title: 'Hidden Property 1' } },
              { listing_details: { visible: true, uuid: 'visible-2', title: 'Visible Property 2' } },
              { listing_details: { visible: false, uuid: 'hidden-2', title: 'Hidden Property 2' } },
              { listing_details: { visible: true, uuid: 'visible-3', title: 'Visible Property 3' } }
            ]
          },
          realty_game_details: { game_title: 'Visibility Test' }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      const result = await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      expect(result.gameListings).toHaveLength(3)
      expect(result.totalProperties).toBe(3)
      
      const visibleUuids = result.gameListings.map(listing => listing.listing_details.uuid)
      expect(visibleUuids).toEqual(['visible-1', 'visible-2', 'visible-3'])
      
      const hiddenUuids = ['hidden-1', 'hidden-2']
      hiddenUuids.forEach(uuid => {
        expect(visibleUuids).not.toContain(uuid)
      })
    })

    it('should handle properties with missing visible property', async () => {
      // Arrange
      const gameSlug = 'missing-visible-test'
      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              { listing_details: { visible: true, uuid: 'explicit-visible' } },
              { listing_details: { uuid: 'no-visible-property' } }, // Missing visible property
              { listing_details: { visible: false, uuid: 'explicit-hidden' } }
            ]
          },
          realty_game_details: { game_title: 'Missing Visible Test' }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      const result = await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      // Only the explicitly visible property should be included
      expect(result.gameListings).toHaveLength(1)
      expect(result.gameListings[0].listing_details.uuid).toBe('explicit-visible')
    })
  })

  describe('store integration', () => {
    it('should call setRealtyGameData with correct structure', async () => {
      // Arrange
      const gameSlug = 'store-integration-test'
      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              { listing_details: { visible: true, uuid: 'test-property' } }
            ],
            default_currency: 'USD'
          },
          realty_game_details: {
            game_title: 'Store Integration Test',
            game_description: 'Testing store integration',
            game_bg_image_url: 'https://example.com/store-bg.jpg'
          }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      expect(mockStore.setRealtyGameData).toHaveBeenCalledTimes(1)
      
      const storeCallArg = mockStore.setRealtyGameData.mock.calls[0][0]
      expect(storeCallArg).toMatchObject({
        gameListings: expect.any(Array),
        gameTitle: expect.any(String),
        gameDesc: expect.any(String),
        gameBgImageUrl: expect.any(String),
        gameDefaultCurrency: expect.any(String),
        totalProperties: expect.any(Number),
        currentProperty: null,
        isDataLoaded: true
      })
    })

    it('should return the same data that was set in the store', async () => {
      // Arrange
      const gameSlug = 'return-consistency-test'
      const apiResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              { listing_details: { visible: true, uuid: 'consistency-test' } }
            ]
          },
          realty_game_details: {
            game_title: 'Consistency Test'
          }
        }
      }

      mockedAxios.get.mockResolvedValue(apiResponse)

      // Act
      const result = await fetchAndSetGameData(gameSlug, mockStore)

      // Assert
      const storeCallArg = mockStore.setRealtyGameData.mock.calls[0][0]
      expect(result).toEqual(storeCallArg)
    })
  })
})
