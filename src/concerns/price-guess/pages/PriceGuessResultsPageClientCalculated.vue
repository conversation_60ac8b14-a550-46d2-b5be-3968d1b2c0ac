<template>
  <div class="price-guess-results-page">
    <div class="max-ctr q-pa-lg">
      <!-- Loading State -->
      <div v-if="isLoading"
           class="loading-container text-center q-pa-xl">
        <q-spinner color="primary"
                   size="3em" />
        <div class="q-mt-md text-h6">Loading Results...</div>
        <div class="text-body2 text-grey-7">Fetching your game results and comparisons</div>
      </div>

      <!-- Error State -->
      <div v-else-if="error"
           class="error-container text-center q-pa-xl">
        <q-icon name="error"
                color="negative"
                size="3em" />
        <div class="q-mt-md text-h6 text-negative">Failed to Load Results</div>
        <div class="text-body2 text-grey-7 q-mb-lg">{{ error }}</div>
        <q-btn color="primary"
               label="Try Again"
               @click="loadResults" />
      </div>

      <!-- Results Content -->
      <div v-else-if="gameResults.length > 0"
           class="results-content">
        <!-- Results Header -->
        <div class="results-header q-mb-xl text-center">
          <div class="performance-badge q-mb-lg">
            <q-icon :name="performanceRating.icon"
                    :color="performanceRating.color"
                    size="4em" />
            <div class="text-h4 text-weight-bold q-mt-md"
                 :class="`text-${performanceRating.color}`">
              {{ performanceRating.rating }}
            </div>
            <div class="text-h6 text-grey-7">{{ totalScore }} / {{ maxPossibleScore }} points</div>
          </div>

          <h1 class="text-h4 text-weight-bold text-primary q-mb-sm">Challenge Complete!</h1>
          <p class="text-body1 text-grey-7">Here's how you performed on each property</p>

          <!-- Session Info -->
          <div class="session-info q-mt-md">
            <!-- <q-chip color="grey-4"
                    text-color="grey-8"
                    icon="fingerprint">
              Session: {{ routeSssnId.substring(0, 8) }}...
            </q-chip>
            <q-chip color="grey-4"
                    text-color="grey-8"
                    icon="schedule">
              {{ formatDate(sessionDate) }}
            </q-chip> -->
          </div>
        </div>

        <!-- Results Table -->
        <ResultsTable :rows="gameResults"
                      :columns="resultsColumns"
                      row-key="id"
                      :pagination="{ rowsPerPage: 0 }"
                      :format-guess="formatGuessPrice"
                      :format-actual="formatActualPrice"
                      :get-score-color="getScoreColor"
                      score-display-type="circular"
                      :field-mappings="{
                        propertyTitle: 'estimate_title',
                        propertyVicinity: 'estimate_vicinity',
                        guessedPrice: 'estimated_price_cents',
                        actualPrice: 'price_at_time_of_estimate_cents',
                        currency: 'estimate_currency',
                        percentageDiff: 'percentage_above_or_below',
                        score: 'estimate_details.game_score'
                      }" />

        <!-- Comparison Summary -->
        <GuessingGameCompSummary :comparison-data="comparisonData"
                                 :is-current-user-session="true"
                                 :is-loading="isLoadingComparisons"
                                 loading-text="Loading comparison data..."
                                 custom-title="How You Compare to Other Players"
                                 :format-price="formatPrice"
                                 :field-mappings="{
                                   propertyTitle: 'property_title',
                                   propertyVicinity: 'property_vicinity',
                                   yourGuess: 'your_guess',
                                   averageGuess: 'average_guess',
                                   actualPrice: 'actual_price',
                                   currency: 'currency',
                                   rankingColor: 'ranking_color',
                                   rank: 'ranking',
                                   totalPlayers: 'total_players',
                                   performanceText: 'performance_text',
                                   priceEstimatesSummary: 'price_estimates_summary'
                                 }" />

        <!-- Actions -->
        <!-- <div class="results-actions text-center">
          <q-btn color="primary"
                 label="Play Again"
                 icon="refresh"
                 size="lg"
                 rounded
                 unelevated
                 @click="playAgain"
                 class="q-mr-md" />
          <q-btn color="secondary"
                 label="Share Results"
                 icon="share"
                 size="lg"
                 rounded
                 outline
                 @click="shareResults" />
        </div> -->
      </div>

      <!-- No Results -->
      <div v-else
           class="no-results-container text-center q-pa-xl">
        <q-icon name="search_off"
                color="grey-5"
                size="3em" />
        <div class="q-mt-md text-h6 text-grey-7">No Results Found</div>
        <div class="text-body2 text-grey-6 q-mb-lg">
          We couldn't find any results for this game session.
        </div>
        <q-btn color="primary"
               label="Start New Game"
               @click="startNewGame" />
      </div>

      <!-- <div v-if="gameCommunitiesDetails.show">
        <div class="results-actions text-center">

          <q-btn color="primary"
                 label="Play Again"
                 icon="refresh"
                 size="lg"
                 rounded
                 unelevated
                 @click="playAgain"
                 class="q-mr-md" />
          <q-btn color="secondary"
                 label="Share Results"
                 icon="share"
                 size="lg"
                 rounded
                 outline
                 @click="shareResults" />
        </div>
      </div> -->

      <q-no-ssr>
        <div v-if="gameCommunitiesDetails.show"
             class="results-actions q-pa-md text-center bg-grey-2 rounded-borders shadow-2">
          <!-- <div class="q-mb-md">
            <q-icon name="fab fa-reddit-alien"
                    size="sm"
                    color="red-6"
                    class="q-mr-sm" />
            <span class="text-subtitle1 text-grey-8">Join the discussion on Reddit:</span>
            <a :href="gameCommunitiesDetails.redditCommunity.url"
               target="_blank"
               class="text-blue-7 hover:text-blue-9 q-ml-sm text-weight-medium">
              {{ gameCommunitiesDetails.redditCommunity.name || gameCommunitiesDetails.redditCommunity.url }}
            </a>
          </div>

          <div class="q-mt-lg">
            <h6 class="text-h6 text-grey-9 q-mb-sm">Other price guess games you might like:</h6>
            <div class="row justify-center q-gutter-sm">
              <div v-for="(game, index) in gameCommunitiesDetails.relevantGames"
                   :key="index"
                   class="col-auto">
                <q-chip :to="game"
                        target="_blank"
                        clickable
                        color="blue-grey-1"
                        text-color="blue-8"
                        class="hover:bg-blue-3">
                  <a :href="`https://${game}`">
                    {{ `https://${game}` }}
                  </a>
                </q-chip>
              </div>
            </div>
          </div> -->

          <!-- Action Buttons -->
          <!-- <div class="q-mt-lg row justify-center q-gutter-md">
            <q-btn color="primary"
                   label="Play Again"
                   icon="refresh"
                   size="lg"
                   rounded
                   unelevated
                   @click="playAgain" />
            <q-btn color="secondary"
                   label="Share Results"
                   icon="share"
                   size="lg"
                   rounded
                   outline
                   @click="shareResults" />
          </div> -->

          <q-card-section class="text-center">
            <div class="text-h6 q-mb-sm">
              🎮 Join the Community Discussion
            </div>
            <q-btn :label="gameCommunitiesDetails.redditCommunity.url"
                   :href="gameCommunitiesDetails.redditCommunity.url"
                   type="a"
                   color="red"
                   target="_blank"
                   icon="mdi-reddit"
                   flat />
          </q-card-section>

          <!-- <q-separator spaced /> -->

          <q-card-section>
            <div class="text-subtitle1 q-mb-sm text-center">
              🧠 Other Price Guess Games You Might Like
            </div>
            <q-list bordered
                    separator
                    class="rounded-borders">
              <q-item v-for="(relevantGame, index) in gameCommunitiesDetails.relevantGames"
                      :key="index"
                      clickable>
                <q-item-section>
                  <a :href="`https://${relevantGame}`"
                     target="_blank"
                     class="text-primary">
                    {{ `https://${relevantGame}` }}
                  </a>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
        </div>
      </q-no-ssr>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { usePriceGuessResults } from '../composables/usePriceGuessResults'
import ResultsTable from 'src/components/common/ResultsTable.vue'
import GuessingGameCompSummary from 'src/components/common/GuessingGameCompSummary.vue'

// Props
const props = defineProps({
  routeSssnId: {
    type: String,
    required: true
  }
})

const $route = useRoute()
const $router = useRouter()
const $q = useQuasar()

// Get dossier UUID from route params
// const dossierUuid = computed(() => $route.params.dossierUuid)

// Initialize the results composable
const {
  isLoading,
  error,
  gameResults,
  comparisonData,
  isLoadingComparisons,
  totalScore,
  maxPossibleScore,
  performanceRating,
  sessionDate,
  loadGameResults,
  loadComparisonData,
  formatPrice,
  getScoreColor,
  formatDate
} = usePriceGuessResults()

// Computed properties
const gameCommunitiesDetails = computed(() => {
  if (typeof window !== 'undefined') {
    const currentHost = location.host // 'david.lvh.me:9100';
    const hosts = ['nuneaton.propertysquares.com', 'brum.propertysquares.com', 'brum-houses.propertysquares.com'];
    const relevantGames = hosts.filter(host => host !== currentHost);
    let redC = {
      url: 'https://www.reddit.com/r/propertysquares/',
      text: 'Join the discussion on reddit!'
    }
    // if (currentHost === 'david.lvh.me:9100') {
    //   redC.url = "https://www.reddit.com/r/brum/"
    // }
    if (currentHost === 'nuneaton.propertysquares.com') {
      redC.url = "https://www.reddit.com/r/nuneaton/"
    }
    return {
      show: true,
      redditCommunity: redC,
      // tiktok? insta? via Madrid gabby??
      relevantGames: relevantGames
    }

  } else {
    return {
      show: false
    }
  }
})
const resultsColumns = computed(() => [
  {
    name: 'property',
    label: 'Property',
    field: 'property',
    align: 'left',
    style: 'width: 30%'
  },
  {
    name: 'guess',
    label: 'Your Guess',
    field: 'guess',
    align: 'right',
    style: 'width: 20%'
  },
  {
    name: 'actual',
    label: 'Actual Price',
    field: 'actual',
    align: 'right',
    style: 'width: 20%'
  },
  {
    name: 'difference',
    label: 'Difference',
    field: 'difference',
    align: 'center',
    style: 'width: 15%'
  },
  {
    name: 'score',
    label: 'Score',
    field: 'score',
    align: 'center',
    style: 'width: 15%'
  }
])

// Methods
const loadResults = async () => {
  try {
    await loadGameResults(props.routeSssnId)
    await loadComparisonData(props.routeSssnId)
  } catch (err) {
    console.error('Failed to load results:', err)
  }
}

const playAgain = () => {
  $router.push({
    name: 'rPriceGuessStart',
    params: {
      // // dossierUuid: dossierUuid.value
    }
  })
}

const startNewGame = () => {
  $router.push({
    name: 'rPriceGuessStart',
    params: {
      // // dossierUuid: dossierUuid.value
    }
  })
}

const shareResults = () => {
  const url = window.location.href
  const text = `I just completed the Property Price Challenge! Score: ${totalScore.value}/${maxPossibleScore.value} (${performanceRating.value.rating})`

  if (navigator.share) {
    navigator.share({
      title: 'Property Price Challenge Results',
      text: text,
      url: url
    }).catch(() => {
      fallbackShare(url, text)
    })
  } else {
    fallbackShare(url, text)
  }
}

const fallbackShare = (url, text) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(url).then(() => {
      $q.notify({
        message: 'Results URL copied to clipboard!',
        icon: 'content_copy',
        color: 'positive'
      })
    })
  }
}

// Initialize on mount
onMounted(() => {
  loadResults()
})

// Helper functions for ResultsTable component
const formatGuessPrice = (row) => {
  return formatPrice(row.estimated_price_cents, row.estimate_currency)
}

const formatActualPrice = (row) => {
  return formatPrice(row.price_at_time_of_estimate_cents, row.estimate_currency)
}
</script>

<style scoped>
.price-guess-results-page {
  /* background-color: #fafafa; */
  min-height: 100vh;
}

.max-ctr {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container,
.error-container,
.no-results-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.results-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.performance-badge {
  display: inline-block;
  padding: 2rem;
  /* background: #f8f9fa; */
  border-radius: 16px;
  border: 2px solid #e9ecef;
}

.session-info {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.property-comparison {
  border-left: 4px solid #e0e0e0;
  padding-left: 1rem;
}

.comparison-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 0.5rem;
}

.stat-card {
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.performance-ranking {
  /* background: #f0f4f8; */
  border-radius: 8px;
  padding: 1rem;
}

.ranking-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.results-actions {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Button improvements */
.q-btn {
  text-transform: none;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .max-ctr {
    padding: 1rem;
  }

  .results-header {
    padding: 1rem;
  }

  .session-info {
    flex-direction: column;
    align-items: center;
  }

  .comparison-stats .row {
    flex-direction: column;
  }

  .ranking-info {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
