<template>
  <q-footer
    v-if="timeToShowFooter"
    elevated
    class="hpg-gate-foot text-white bg-gradient"
  >
    <q-toolbar>
      <q-toolbar-title>
        <div class="text-center q-pa-sm text-body1">
          <div>
            <router-link
              class="text-white"
              :to="{ name: 'rInfoPage', params: { page_slug: 'privacy' } }"
            >
              Privacy Policy
            </router-link>

            |

            <router-link
              class="text-white"
              :to="{
                name: 'rInfoPage',
                params: { page_slug: 'terms-of-service' },
              }"
            >
              Terms Of Service
            </router-link>
          </div>

          <div class="row justify-center items-center q-gutter-sm q-mt-sm">
            <q-btn
              type="a"
              href="https://www.linkedin.com/company/house-price-guess/"
              target="_blank"
              rel="noopener"
              aria-label="Follow House Price Guess on LinkedIn"
              icon="mdi-linkedin"
              label="Follow us on LinkedIn"
              class="social-cta"
              rounded
              glossy
              unelevated
              no-caps
              size="sm"
              text-color="white"
              :style="{ backgroundColor: '#0A66C2' }"
            >
              <q-tooltip anchor="top middle" self="bottom middle">
                Follow us on LinkedIn
              </q-tooltip>
            </q-btn>

            <q-btn
              type="a"
              href="https://www.facebook.com/profile.php?id=61577791497626"
              target="_blank"
              rel="noopener"
              aria-label="Like House Price Guess on Facebook"
              icon="mdi-facebook"
              label="Like us on Facebook"
              class="social-cta"
              rounded
              glossy
              unelevated
              no-caps
              size="sm"
              text-color="white"
              :style="{ backgroundColor: '#1877F2' }"
            >
              <q-tooltip anchor="top middle" self="bottom middle">
                Like us on Facebook
              </q-tooltip>
            </q-btn>

            <q-btn
              type="a"
              href="https://www.reddit.com/r/housepriceguess/"
              target="_blank"
              rel="noopener"
              aria-label="Join House Price Guess on Reddit"
              icon="mdi-reddit"
              label="Join us on Reddit"
              class="social-cta"
              rounded
              glossy
              unelevated
              no-caps
              size="sm"
              text-color="white"
              :style="{ backgroundColor: '#FF4500' }"
            >
              <q-tooltip anchor="top middle" self="bottom middle">
                Join us on Reddit
              </q-tooltip>
            </q-btn>

          </div>
        </div>

          <!-- |

          <router-link class="text-white"
                       :to="{
                        name: 'rH2cLoginPage',
                        params: {},
                      }">
            Sign In
          </router-link>

          |

          <router-link class="text-white"
                       :to="{
                        name: 'rCreateAccount',
                        params: {},
                      }">
            Create An Account
          </router-link> -->

          <!-- |

          <router-link
            class="text-white"
            :to="{
              name: 'rInfoPage',
              params: { page_slug: 'for-home-buyers' },
            }"
          >
            For Home Buyers
          </router-link>

          |

          <router-link
            class="text-white"
            :to="{
              name: 'rInfoPage',
              params: { page_slug: 'for-home-sellers' },
            }"
          >
            For Home Sellers
          </router-link>

          |

          <router-link
            class="text-white"
            :to="{
              name: 'rInfoPage',
              params: { page_slug: 'for-estate-agents' },
            }"
          >
            For Estate Agents
          </router-link> -->
          <!--  -->

      </q-toolbar-title>
    </q-toolbar>
    <div class="q-pa-sm">
      <div class="copyright-foot width-full">
        <div :class="copywriteClass">
          Copyright © 2021 - 2025
          <a class="text-white" href="/"> {{ whitelabelNameDisplay }} </a>
        </div>
      </div>
    </div>
  </q-footer>
</template>
<script>
import { defineComponent } from 'vue'
export default defineComponent({
  created() {},
  name: 'HtocFooter',
  mounted() {
    setTimeout(() => {
      // footer sometimes loading before rest of page
      // This avoids that
      this.timeToShowFooter = true
      // TODO - investigate if this is affecting pagespeed
      // might want to wrap in a q-no-ssr tag
    }, 500)
  },
  data() {
    return {
      timeToShowFooter: false,
    }
  },
  computed: {
    // configData() {
    //   let configData = currentConfigData()
    //   return configData
    // },
    copywriteClass() {
      if (this.$q.screen.lt.md) {
        return 'text-center'
      } else {
        return 'float-right'
      }
    },
  },
  props: {
    homeUrl: {
      type: String,
      default: 'https://homestocompare.com/',
    },
    serviceEmail: {
      type: String,
      default: '<EMAIL>',
    },
    whitelabelNameDisplay: {
      type: String,
      default: 'HomesToCompare',
    },
  },
})
</script>


<style scoped>
.social-cta {
  transition: transform 0.15s ease, box-shadow 0.15s ease;
}
.social-cta:hover,
.social-cta:focus {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}
</style>
