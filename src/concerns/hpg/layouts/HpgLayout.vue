<template>
  <q-layout view="lhh LpR ffr" class="hpg-main-layout-outer">
    <!-- <HtocGateMarketingHeader></HtocGateMarketingHeader> -->
    <q-header class="hpg-main-mht-ctr bg-white" reveal elevated>
      <q-toolbar
        style=""
        class="hpg-main-marketing-header-toolbar container max-ctr"
      >
        <q-toolbar-title class="inline-flex items-center">
          <!-- <div class="toolbar-site-label-main">
            <a class="ignore-link" href="https://housepriceguess.com/">
              <img
                class="hpg-logo-img"
                :src="logoUrl"
                alt="HousePriceGuess Logo"
              />
            </a>
          </div> -->

          <!-- Logo -->
          <a href="https://housepriceguess.com/" class="logo-link q-mr-sm">
            <img
              :src="logoUrl"
              alt="House Price Guess Logo"
              class="site-logo hpg-logo-img"
            />
          </a>

          <!-- Site Name -->
          <div class="toolbar-site-label-main">
            <a class="ignore-link" href="https://housepriceguess.com/">
              <div>
                <span class="color-second" style="color: #665df5">HOUSE</span>
                <span style="color: rgb(10, 0, 131)">PRICE</span>
                <span class="color-second" style="color: #665df5">GUESS</span>
              </div>
            </a>
          </div>
        </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container
      :class="[`main-layout-htoc-2024g-gate`, maxCtrClass]"
      style="padding-top: 50px"
    >
      <!-- must be careful not to add :key="$route.fullPath" below!! -->
      <router-view
        @blurbCta="blurbCta"
        :isPriceGuessOnly="true"
        @showNotification="showNotification"
        :whitelabelNameDisplay="whitelabelNameDisplay"
        :serviceEmail="serviceEmail"
      />
    </q-page-container>
    <div class="ajax-bar-container">
      <q-ajax-bar
        ref="bar"
        position="top"
        color="accent"
        size="10px"
        :skip-hijack="false"
        :hijack-filter="loadingBarFilterFn"
      />
    </div>
    <HpgFooter
      homeUrl="https://housepriceguess.com/"
      :whitelabelNameDisplay="whitelabelNameDisplay"
      :serviceEmail="serviceEmail"
    ></HpgFooter>
    <!-- <HtocMobileFooter v-if="showMobileFooter"></HtocMobileFooter>
    <HpgFooter v-else></HpgFooter>
    <NewAccountEnquiryPrompt :showNewAccEnqPrompt="showNewAccEnqPrompt"
                             :selectedAccountPlan="selectedAccountPlan"
                             @blurbCtaEnd="blurbCtaEnd"></NewAccountEnquiryPrompt> -->
  </q-layout>
</template>

<script>
import HpgFooter from 'src/concerns/hpg/components/head-foot/HpgFooter.vue'
//  'src/concerns/dossiers/components/head-foot/HpgFooter.vue'
import { defineComponent, ref, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { useRoute } from 'vue-router'
import useJsonLd from 'src/compose/useJsonLd.js'
import { useMeta } from 'quasar'
import { useRealtyGameMetaStore } from 'src/stores/realtyGameMeta'
import { storeToRefs } from 'pinia'

// Import logo image
import logoUrl from '/icons/favicon-128x128.png'

export default defineComponent({
  name: 'HpgLayout',
  components: {
    HpgFooter,
  },
  data() {
    return {
      showNewAccEnqPrompt: false,
      selectedAccountPlan: 'free',
    }
  },
  props: {
    serviceEmail: {
      type: String,
      default: '<EMAIL>',
    },
    whitelabelNameDisplay: {
      type: String,
      default: 'HousePriceGuess',
    },
  },
  computed: {
    showMobileFooter() {
      return this.$q.platform.is.mobile
    },
    maxCtrClass() {
      if (this.$route.name === 'rSubdomainRoot') {
        return ''
      } else {
        return 'max-ctr'
      }
    },
  },
  methods: {
    blurbCta(selectedAccountPlan) {
      this.selectedAccountPlan = selectedAccountPlan
      this.showNewAccEnqPrompt = true
    },
    blurbCtaEnd() {
      this.showNewAccEnqPrompt = false
    },
  },
  setup() {
    const $q = useQuasar()
    const route = useRoute()

    // Initialize JSON-LD functionality
    const { initializeDefaultJsonLd, updateWebPageSchema, jsonLdScriptTags } =
      useJsonLd()

    // Initialize JSON-LD
    initializeDefaultJsonLd()

    // Update webpage schema based on route
    const routeMeta = route.meta || {}
    updateWebPageSchema({
      title: routeMeta.title,
      description: routeMeta.description,
      keywords: routeMeta.keywords,
    })

    // Meta management from store
    const metaStore = useRealtyGameMetaStore()
    const { title, description, image, url, keywords } = storeToRefs(metaStore)
    useMeta(() => ({
      title: title.value,
      meta: {
        description: { name: 'description', content: description.value },
        keywords: { name: 'keywords', content: keywords.value },
        'og:title': { property: 'og:title', content: title.value },
        'og:description': {
          property: 'og:description',
          content: description.value,
        },
        'og:image': { property: 'og:image', content: image.value },
        'og:url': { property: 'og:url', content: url.value },
        'og:type': { property: 'og:type', content: 'website' },
        'twitter:card': {
          name: 'twitter:card',
          content: 'summary_large_image',
        },
        'twitter:title': { name: 'twitter:title', content: title.value },
        'twitter:description': {
          name: 'twitter:description',
          content: description.value,
        },
        'twitter:image': { name: 'twitter:image', content: image.value },
        'twitter:url': { name: 'twitter:url', content: url.value },
      },
      link: {
        'preconnect-img': { rel: 'preconnect', href: 'https://images.unsplash.com', crossorigin: '' },
        'preconnect-api': { rel: 'preconnect', href: 'http://hpg-scoot.lvh.me:3333' },
        // Fonts preconnects if using Google Fonts
        'preconnect-fonts': { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        'preconnect-fonts-gstatic': { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' },
      },
      script: jsonLdScriptTags.value.map((item) => ({
        type: 'application/ld+json',
        id: item.id,
        innerHTML: item.json,
      })),
    }))

    function showNotification(notificationMessage) {
      $q.notify(notificationMessage)
    }

    return {
      showNotification,
      loadingBarFilterFn(url) {
        return !(url.includes('prop_ev_init') || !url.includes('user'))
      },
      logoUrl, // Expose logoUrl to the template
    }
  },
})
</script>

<style>
.main-layout-hpg-main-2024g {
  /* fix the edit tab disappearing */
  padding-top: 50px;
}
.hpg-logo-img {
  height: 40px; /* Smaller size to fit toolbar */
  width: auto;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  background: #fff;
  padding: 4px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.hpg-logo-img:hover {
  transform: scale(1.05) rotate(-2deg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
@media (max-width: 768px) {
  .hpg-logo-img {
    height: 32px; /* Slightly smaller for mobile */
    padding: 3px;
  }
}
.site-logo {
  height: 32px;
  width: auto;
  vertical-align: middle;
}

.logo-link {
  display: inline-block;
  margin-right: 12px;
}

.inline-flex {
  display: inline-flex;
  align-items: center;
}

.no-wrap {
  white-space: nowrap;
}
</style>
