<template>
  <div class="overflow-hidden">
    <q-resize-observer @resize="onResize" :debounce="100" />

    <q-splitter
      id="photos"
      v-model="splitterModel"
      :limits="[0, 100]"
      :style="splitterStyle"
      before-class="overflow-hidden"
      after-class="overflow-hidden"
    >
      <template v-slot:before>
        <div class="text-h4 q-mb-md">beforrrreee</div>
        <img :src="leftColImage" :width="width" class="absolute-top-left" />
      </template>

      <template v-slot:separator>
        <q-avatar color="primary" text-color="white" size="40px" icon="drag_indicator" />
      </template>
      <template v-slot:after>
        <img :src="rightColImage" :width="width" class="absolute-top-right" />
      </template>
    </q-splitter>
  </div>
</template>

<script>
import { ref, computed } from "vue"

export default {
  props: {
    rightColImage: {
      type: String,
    },
    leftColImage: {
      type: String,
      // default: () => {
      //   title: ""
      // },
    },
  },
  setup() {
    const width = ref(400)
    let pending = false

    function onResize(info) {
      if (pending) return
      pending = true
      requestAnimationFrame(() => {
        width.value = info.width
        pending = false
      })
    }

    return {
      width,
      splitterModel: ref(50), // start at 50%

      splitterStyle: computed(() => ({
        // height: "800px", //
        height: Math.min(600, 0.66 * width.value) + "px",
        width: width.value + "px",
      })),

      // we are using QResizeObserver to keep
      // this example mobile-friendly
      onResize,
    }
  },
}
</script>
