<template>
  <div class="overflow-hidden comp-images2-in col-xs-12" style="max-width: 98vw">
    <q-resize-observer @resize="onResize" :debounce="100" />

    <q-splitter
      id="photos"
      v-model="splitterModel"
      @update:model-value="splitChangeHandler"
      :limits="[0, 100]"
      :style="splitterStyle"
      before-class="overflow-hidden"
      after-class="overflow-hidden"
    >
      <template v-slot:before>
        <!-- <div class="text-h4 q-mb-md">beforrrreee</div> -->
        <q-img
          @click="imagesSeparatorClicked"
          fit="cover"
          height="100%"
          :width="width.toString()"
          :src="leftColImage"
          class="left-img"
        >
          <ConvertableCurrencyRibbon
            v-if="showCurrRibbon"
            :isLeft="true"
            :priceInCents="leftColPriceInCents"
            :originalCurrency="'GBP'"
          >
          </ConvertableCurrencyRibbon>
          <!-- <div
            class="ribbon-ctr left-ribbon"
            data-ribbon="£20,000,000"
            style="--d: 8px; --c: red; background: transparent; margin: 6px"
          ></div> -->
          <div
            v-if="showLeftTeaser"
            style="padding: 3px"
            class="left-col-teaser q-py-none absolute-bottom text-subtitle2 flex flex-center caption-left"
          >
            <router-link :to="routeShowSide('left')" custom v-slot="{ navigate }">
              <div style="cursor: pointer" role="link" @click="navigate">
                <div class="q-mt-sm text-body1 text-center text-white">
                  {{ leftColTeaser || "Property A" }}
                </div>
              </div>
            </router-link>
          </div>
        </q-img>
        <!-- <img :src="leftColImage" :width="width" class="absolute-top-left" /> -->
      </template>

      <template v-slot:separator>
        <div>
          <q-avatar
            @click="imagesSeparatorClicked"
            text-color="white"
            size="60px"
            class="rotate-90 comp-imgs-2-avt h2caccent"
            icon="unfold_more"
          />
        </div>
        <!-- <q-avatar
          color="primary"
          text-color="black"
          size="40px"
          icon="unfold_more_vertical"
        /> -->
      </template>
      <template v-slot:after>
        <q-img
          @click="imagesSeparatorClicked"
          fit="cover"
          height="100%"
          :src="rightColImage"
          :width="width.toString()"
          class="right-img"
        >
          <ConvertableCurrencyRibbon
            v-if="showCurrRibbon"
            :priceInCents="rightColPriceInCents"
            :originalCurrency="'GBP'"
          >
          </ConvertableCurrencyRibbon>
          <div
            v-if="showRightTeaser"
            style="padding: 3px"
            class="right-col-teaser q-py-none absolute-bottom text-subtitle2 flex flex-center caption-right"
          >
            <router-link :to="routeShowSide('right')" custom v-slot="{ navigate }">
              <div style="cursor: pointer" role="link" @click="navigate">
                <div class="q-mt-sm text-body1 text-center text-white">
                  {{ rightColTeaser || "Property B" }}
                </div>
              </div>
            </router-link>
          </div>
        </q-img>
        <!-- <img :src="rightColImage"  /> -->
      </template>
    </q-splitter>
  </div>
</template>

<script>
import { ref, computed } from "vue"
import ConvertableCurrencyRibbon from "components/widgets/ConvertableCurrencyRibbon.vue"
export default {
  components: {
    ConvertableCurrencyRibbon,
  },
  data() {
    return {
      clicksCount: 0,
    }
  },
  props: {
    routeShowSideBySide: {
      type: Object,
      default: () => {},
    },
    // enableLinks: {
    //   type: Boolean,
    //   default: false,
    // },
    leftColPriceInCents: {
      type: Number,
    },
    rightColPriceInCents: {
      type: Number,
    },
    // comparisonDetails: {
    //   type: Object,
    //   default: () => {},
    // },
    listItemUuid: {
      type: String,
    },
    compUuid: {
      type: String,
    },
    leftColTeaser: {
      type: String,
    },
    rightColTeaser: {
      type: String,
    },
    rightColImage: {
      type: String,
    },
    leftColImage: {
      type: String,
      // default: () => {
      //   title: ""
      // },
    },
    comparisonsHeight: {
      type: Number,
      default: 300,
    },
  },
  methods: {
    imagesSeparatorClicked(event) {
      if (this.splitterModel < 45) {
        //   this.splitterModel = 50
        // } else if (this.splitterModel === 50) {
        this.splitterModel = 80
      } else {
        this.splitterModel = 20
      }
      this.clicksCount += 1
      if (this.clicksCount > 3) {
        let routeDetails = this.routeShowSideBySide
        if (this.$route.name !== "rPublicSideBySideCompare") {
          this.$router.push(routeDetails)
        }
      }
    },
    // leftTeaserClicked() {
    //   this.splitterModel = 80
    // },
    // rightTeaserClicked() {
    //   this.splitterModel = 20
    // },
    splitChangeHandler(newSplit) {
      // if (newSplit < 45) {
      //   this.rightImgCaptionStyle = "display:none;"
      //   this.leftImgCaptionStyle = "display:flex;"
      // }
      // if (newSplit > 55) {
      //   this.rightImgCaptionStyle = "display:flex;"
      //   this.leftImgCaptionStyle = "display:none;"
      // }
    },
    routeShowSide(propertySide) {
      let routeShowSide = {
        name: "rPublicSideBySideCompareWithSide",
        params: {
          propertySide: propertySide,
          comparisonUuid: this.compUuid,
        },
      }
      if (this.listItemUuid) {
        routeShowSide.params.listItemUuid = this.listItemUuid
      }
      return routeShowSide
    },
  },
  computed: {
    showCurrRibbon() {
      if (this.splitterModel !== 50) {
        return true
      } else {
        return false
      }
    },
    showLeftTeaser() {
      if (this.splitterModel < 51) {
        return false
      } else {
        return this.leftColTeaser
      }
    },
    showRightTeaser() {
      if (this.splitterModel > 49) {
        return false
      } else {
        return this.rightColTeaser
      }
    },
    splitterStyle() {
      let splitterStyle = {
        height: Math.min(this.comparisonsHeight, 0.96 * this.width) + "px",
        width: this.width + "px",
      }
      return splitterStyle
    },
  },
  setup() {
    const width = ref(400)
    let pending = false

    function onResize(info) {
      if (pending) return
      pending = true
      requestAnimationFrame(() => {
        width.value = info.width
        pending = false
      })
    }

    return {
      width,
      splitterModel: ref(50), // start at 50%

      // splitterStyle: computed(() => ({
      //   height: Math.min(600, 0.66 * width.value) + "px",
      //   width: width.value + "px",
      // })),

      // we are using QResizeObserver to keep
      // this example mobile-friendly
      onResize,
    }
  },
}
</script>
