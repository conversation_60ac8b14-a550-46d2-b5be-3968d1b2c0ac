import { defineStore } from 'pinia'

/**
 * Pinia store for managing the Realty Game state
 * 
 * This store handles:
 * - Property listings for the game
 * - Game metadata (title, description, background image)
 * - Current property tracking
 * - Data loading state
 * 
 * @see README-realtyGame.md for detailed documentation
 */
export const useRealtyGameStore = defineStore('realtyGame', {
  state: () => ({
    /** @type {Array} Array of property objects for the game */
    gameListings: [],
  /**
   * Map of Realty Game Listing (RGL) data keyed by the listing-in-game UUID
   * Each entry mirrors the backend realty_game_listing JSON (eg ordered_photo_uuids, visible_photo_uuids, gl_* attrs)
   * @type {Record<string, any>} */
  realtyGameListings: {},
    /** @type {string} Display title for the game */
    gameTitle: 'Property Price Challenge',
    /** @type {string} Description text for the game */
    gameDesc: '',
    /** @type {string} URL for the game's background image */
    gameBgImageUrl: '',
  /** @type {string} Default currency for the game (from backend) */
  gameDefaultCurrency: 'GBP',
    /** @type {Object|null} Currently selected/viewed property */
    currentProperty: null,
    /** @type {boolean} Flag indicating if game data has been loaded */
    isDataLoaded: false,
  }),
  actions: {
    /**
     * Sets the complete game data, replacing all current state
     * @param {Object} data - Game data object
     * @param {Array} [data.gameListings=[]] - Array of property objects
     * @param {string} [data.gameTitle='Property Price Challenge'] - Game display title
     * @param {string} [data.gameDesc=''] - Game description
     * @param {string} [data.gameBgImageUrl=''] - Background image URL
     * @param {Object} [data.currentProperty=null] - Currently selected property
     */
    setRealtyGameData(data) {
      this.gameListings = data.gameListings || []
      this.gameTitle = data.gameTitle || 'Property Price Challenge'
      this.gameDesc = data.gameDesc || ''
      this.gameBgImageUrl = data.gameBgImageUrl || ''
  this.gameDefaultCurrency = data.gameDefaultCurrency || 'GBP'
      this.currentProperty = data.currentProperty || null
      this.isDataLoaded = true
      // Optionally seed any provided RGL data
      if (data.realtyGameListings && typeof data.realtyGameListings === 'object') {
        this.realtyGameListings = { ...this.realtyGameListings, ...data.realtyGameListings }
      }
    },
    /**
     * Save a single realty_game_listing payload keyed by its UUID (listing-in-game UUID)
     * @param {string} rglUuid
     * @param {Object} rglData
     */
    setRealtyGameListing(rglUuid, rglData) {
      if (!rglUuid || !rglData) return
      this.realtyGameListings = { ...this.realtyGameListings, [rglUuid]: rglData }
    },
    /**
     * Bulk set RGL records
     * @param {Record<string, any>} rglMap
     */
    setRealtyGameListings(rglMap) {
      if (!rglMap) return
      this.realtyGameListings = { ...this.realtyGameListings, ...rglMap }
    },
    /**
     * Resets all game data to initial state
     */
    clearRealtyGameData() {
      this.gameListings = []
      this.realtyGameListings = {}
      this.gameTitle = 'Property Price Challenge'
      this.gameDesc = ''
      this.gameBgImageUrl = ''
  this.gameDefaultCurrency = 'GBP'
      this.currentProperty = null
      this.isDataLoaded = false
    },
    /**
     * Retrieves a property by its array index
     * @param {number} index - Zero-based index of the property
     * @returns {Object|null} Property object if found, null otherwise
     */
    getPropertyByIndex(index) {
      return this.gameListings[index] || null
    },
    /**
     * Retrieves a property by its unique identifier
     * @param {string} uuid - Unique identifier of the property
     * @returns {Object|null} Property object if found, null otherwise
     */
    getPropertyByUuid(uuid) {
      return this.gameListings.find((prop) => prop.uuid === uuid) || null
    },
  },
  getters: {
    /** @returns {Array} Array of game property listings */
    getGameListings: (state) => state.gameListings,
  /**
   * Getter to access a specific realty_game_listing by UUID
   * @returns {(uuid: string) => Object|null}
   */
  getRealtyGameListingByUuid: (state) => (uuid) => state.realtyGameListings[uuid] || null,
  /** @returns {Record<string, any>} Entire map of RGL records */
  getAllRealtyGameListings: (state) => state.realtyGameListings,
    /** @returns {string} Current game title */
    getGameTitle: (state) => state.gameTitle,
    /** @returns {string} Current game description */
    getGameDesc: (state) => state.gameDesc,
    /** @returns {string} Background image URL */
    getGameBgImageUrl: (state) => state.gameBgImageUrl,
  /** @returns {string} Default currency for the game */
  getGameDefaultCurrency: (state) => state.gameDefaultCurrency,
    /** @returns {Object|null} Currently selected property */
    getCurrentProperty: (state) => state.currentProperty,
    /** @returns {number} Total number of properties in the game */
    getTotalProperties: (state) => state.gameListings.length,
    /** @returns {boolean} Whether game data has been loaded */
    getIsDataLoaded: (state) => state.isDataLoaded,
    /** @returns {Object|null} First property in the listings array, or null if empty */
    firstVisibleListing: (state) => state.gameListings[0] || null,
  },
})
