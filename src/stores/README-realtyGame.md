# Realty Game Store

The `useRealtyGameStore` is a Pinia store that manages the state for the Property Price Challenge game. This store handles game data, property listings, and provides methods for property retrieval and game state management.

## Table of Contents

- [Overview](#overview)
- [State Structure](#state-structure)
- [Actions](#actions)
- [Getters](#getters)
- [Usage Examples](#usage-examples)
- [Data Flow](#data-flow)
- [Testing](#testing)

## Overview

The Realty Game Store is designed to:
- Store and manage property listings for the game
- Handle game metadata (title, description, background image)
- Track the current property being viewed
- Provide convenient methods for property lookup
- Manage the game's loading state

## State Structure

```javascript
{
  gameListings: [],           // Array of property objects
  gameTitle: 'Property Price Challenge',  // Game display title
  gameDesc: '',               // Game description
  gameBgImageUrl: '',         // Background image URL for game UI
  gameDefaultCurrency: 'GBP', // Default currency for the game (from backend)
  currentProperty: null,      // Currently selected/viewed property
  isDataLoaded: false        // Flag indicating if game data has been loaded
}
```

### Property Object Structure

Each property in `gameListings` should have the following structure:

```javascript
{
  uuid: 'unique-property-id',
  title: 'Property Title',
  description: 'Property description',
  city: 'City Name',
  postal_code: 'Postal Code',
  region: 'Region/State',
  country: 'Country',
  street_address: 'Street Address',
  price_sale_current_cents: 50000000, // Price in cents
  currency: 'GBP',
  count_bedrooms: 3,
  count_bathrooms: 2,
  count_garages: 1,
  latitude: 51.5074,
  longitude: -0.1278,
  photos: [/* photo objects */]
  // ... other property fields
}
```

## Actions

### `setRealtyGameData(data)`

Sets the complete game data, replacing all current state.

**Parameters:**
- `data` (Object): Game data object containing:
  - `gameListings` (Array, optional): Array of property objects
  - `gameTitle` (String, optional): Game title (defaults to 'Property Price Challenge')
  - `gameDesc` (String, optional): Game description (defaults to empty string)
  - `gameBgImageUrl` (String, optional): Background image URL (defaults to empty string)
  - `gameDefaultCurrency` (String, optional): Default currency code for this game (defaults to 'GBP')
  - `currentProperty` (Object, optional): Current property object (defaults to null)

**Example:**
```javascript
store.setRealtyGameData({
  gameListings: [property1, property2, property3],
  gameTitle: 'London Property Challenge',
  gameDesc: 'Test your knowledge of London property prices!',
  gameBgImageUrl: 'https://example.com/london-bg.jpg',
  currentProperty: property1
})
```

### `clearRealtyGameData()`

Resets all game data to initial state.

**Example:**
```javascript
store.clearRealtyGameData()
// All state returns to initial values
```

### `getPropertyByIndex(index)`

Retrieves a property by its array index.

**Parameters:**
- `index` (Number): Zero-based index of the property

**Returns:**
- Property object if found, `null` otherwise

**Example:**
```javascript
const firstProperty = store.getPropertyByIndex(0)
const invalidProperty = store.getPropertyByIndex(999) // Returns null
```

### `getPropertyByUuid(uuid)`

Retrieves a property by its unique identifier.

**Parameters:**
- `uuid` (String): Unique identifier of the property

**Returns:**
- Property object if found, `null` otherwise

**Example:**
```javascript
const property = store.getPropertyByUuid('prop-123')
if (property) {
  console.log(property.title)
}
```

## Getters

### `getGameListings`
Returns the array of game property listings.

### `getGameTitle`
Returns the current game title.

### `getGameDesc`
Returns the current game description.

### `getGameBgImageUrl`
Returns the background image URL.

### `getGameDefaultCurrency`
Returns the default currency code to use for this game.

### `getCurrentProperty`
Returns the currently selected property.

### `getTotalProperties`
Returns the total number of properties in the game.

### `getIsDataLoaded`
Returns whether game data has been loaded.

### `firstVisibleListing`
Returns the first property in the listings array, or `null` if empty.

## Usage Examples

### Basic Setup

```javascript
import { useRealtyGameStore } from 'src/stores/realtyGame'

// In a Vue component
export default {
  setup() {
    const realtyGameStore = useRealtyGameStore()
    
    return {
      realtyGameStore
    }
  }
}
```

### Loading Game Data

```javascript
// Load game data from API
const gameData = await fetchGameData()
realtyGameStore.setRealtyGameData(gameData)

// Check if data is loaded
if (realtyGameStore.getIsDataLoaded) {
  console.log(`Loaded ${realtyGameStore.getTotalProperties} properties`)
}
```

### Property Navigation

```javascript
// Get current property
const currentProp = realtyGameStore.getCurrentProperty

// Navigate to next property by index
const nextIndex = currentIndex + 1
const nextProperty = realtyGameStore.getPropertyByIndex(nextIndex)

if (nextProperty) {
  // Update current property
  realtyGameStore.setRealtyGameData({
    ...realtyGameStore.$state,
    currentProperty: nextProperty
  })
}
```

### Finding Specific Properties

```javascript
// Find property by UUID (useful for URL routing)
const propertyId = route.params.id
const property = realtyGameStore.getPropertyByUuid(propertyId)

if (!property) {
  // Handle property not found
  router.push('/game/not-found')
}
```

### Reactive UI Updates

```javascript
// In a Vue template
<template>
  <div>
    <h1>{{ realtyGameStore.getGameTitle }}</h1>
    <p>{{ realtyGameStore.getGameDesc }}</p>
    
    <div v-if="realtyGameStore.getIsDataLoaded">
      <p>Properties loaded: {{ realtyGameStore.getTotalProperties }}</p>
      
      <div v-if="realtyGameStore.firstVisibleListing">
        <h2>{{ realtyGameStore.firstVisibleListing.title }}</h2>
      </div>
    </div>
    
    <div v-else>
      Loading game data...
    </div>
  </div>
</template>
```

## Data Flow

1. **Initialization**: Store starts with default empty state
2. **Data Loading**: `setRealtyGameData()` populates the store with game data
3. **Property Access**: Components use getters and actions to access properties
4. **State Updates**: Actions modify the state (e.g., changing current property)
5. **Cleanup**: `clearRealtyGameData()` resets state when needed

```mermaid
graph TD
    A[Component] -->|fetchGameData| B[API/Service]
    B -->|gameData| C[setRealtyGameData]
    C --> D[Store State Updated]
    D --> E[Reactive UI Updates]
    
    A -->|getPropertyByUuid| F[Store Getter]
    A -->|getPropertyByIndex| F
    F --> G[Property Data]
    G --> E
    
    A -->|clearRealtyGameData| H[Reset State]
    H --> D
```

## Testing

The store includes comprehensive tests covering:

- **Initial State**: Verifies correct default values
- **Data Setting**: Tests `setRealtyGameData` with various inputs
- **Data Clearing**: Tests `clearRealtyGameData` reset functionality
- **Property Retrieval**: Tests both index and UUID-based property lookup
- **Getters**: Verifies all computed properties work correctly
- **Edge Cases**: Tests with large datasets, special characters, and invalid inputs

### Running Tests

```bash
# Run all tests
npm run test

# Run in watch mode
npm run test:watch

# Run specific store tests
npm run test src/stores/__tests__/realtyGame.test.js
```

### Test Coverage

The test suite covers:
- ✅ All actions and their edge cases
- ✅ All getters and computed properties  
- ✅ State management and data integrity
- ✅ Error handling for invalid inputs
- ✅ Performance with large datasets

## Best Practices

1. **Always check if data is loaded** before accessing properties:
   ```javascript
   if (store.getIsDataLoaded && store.getTotalProperties > 0) {
     // Safe to access properties
   }
   ```

2. **Use UUID for property identification** when possible:
   ```javascript
   // Preferred: UUID-based lookup
   const property = store.getPropertyByUuid(propertyId)
   
   // Less reliable: Index-based lookup
   const property = store.getPropertyByIndex(0)
   ```

3. **Handle null returns gracefully**:
   ```javascript
   const property = store.getPropertyByUuid(id)
   if (!property) {
     console.warn(`Property with ID ${id} not found`)
     return
   }
   ```

4. **Clear data when appropriate**:
   ```javascript
   // When navigating away from game
   onBeforeUnmount(() => {
     store.clearRealtyGameData()
   })
   ```

## Migration and Updates

When updating the store structure:

1. **Update tests first** to reflect new expected behavior
2. **Maintain backward compatibility** where possible
3. **Update documentation** to reflect changes
4. **Consider data migration** for existing stored data

## Related Files

- `src/stores/realtyGame.js` - The main store implementation
- `src/stores/__tests__/realtyGame.test.js` - Comprehensive test suite
- `src/test/utils.js` - Test utilities and mock data factories
