import { boot } from 'quasar/wrappers'

// Lazy-load ApexCharts only on routes that need it
let apexLoaded = false
async function ensureApexCharts(app) {
  if (apexLoaded) return
  const { default: VueApexCharts } = await import('vue3-apexcharts')
  app.use(VueApexCharts)
  apexLoaded = true
}

export default boot(({ app, router }) => {
  // Load for initial route if required
  router.isReady().then(async () => {
    if (router.currentRoute.value?.meta?.usesApexCharts) {
      await ensureApexCharts(app)
    }
  })

  // Load on navigation to routes that require ApexCharts
  router.beforeEach(async (to) => {
    if (to.meta?.usesApexCharts) {
      await ensureApexCharts(app)
    }
    return true
  })
})
