// based off:
// /dev/sites-2024-apr/qsr-deep-wed-fe/src/boot/fetch-subdomain-env.js
let pwbFlexConfig = {}
export default async ({ app, ssrContext }) => {
  let subdomainName = 'hpg-scoot'
  if (process.env.CLIENT) {
    // Client-side: use window.location.hostname to get the subdomain
    const hostname = window.location.hostname
    const parts = hostname.split('.')

    const isIpHost = /^\d+(?:\.\d+){3}$/.test(hostname)
    if (!isIpHost && parts.length > 2) {
      subdomainName = parts[0] // Assuming subdomain is the first part
    }
  } else if (process.env.SERVER && ssrContext) {
    // Server-side: use the server-side request to get the hostname
    const hostname = ssrContext.req.headers.host
    const parts = hostname.split('.')

    const isIpHost = /^\d+(?:\.\d+){3}$/.test(hostname)
    if (!isIpHost && parts.length > 2) {
      subdomainName = parts[0] // Assuming subdomain is the first part
    }
  }

  // Set the subdomain as a global property for both client and server
  app.config.globalProperties.$subdomainName = subdomainName

  // You can also log the subdomainName for debugging
  // console.log('Current subdomainName:', subdomainName)

  let subdomainEnv = ''

  if (process.env.SERVER) {
    // For SSR (server-side rendering)
    const { req } = ssrContext
    subdomainEnv = req.headers['x-subdomain-env']
  } else {
    // For client-side (CSR)
    const response = await fetch(window.location.href)
    subdomainEnv = response.headers.get('X-Subdomain-Env')
  }

  // let keyForPublicConf = subdomainEnv || ''
  // if (subdomainName.includes('--')) {
  //   // if the subdomainName has a -- in it,
  //   // then it should override the subdomainEnv
  //   keyForPublicConf = subdomainName
  // }
  // let keysForPublicConf = keyForPublicConf.split('--')
  // let subdomainSlug = keysForPublicConf[0] || subdomainName || ''
  // let localOverRide = keysForPublicConf[1] || ''
  // let optionalPackageCode = keysForPublicConf[2] || 'dsec'
  // // console.log('keysForPublicConf:', keysForPublicConf)

  let prodDataApiBase = `https://${subdomainName}.homestocompare.com`
  let devDataApiBase = `http://${subdomainName}.lvh.me:3333`
  // let devDataApiBase = `https://${subdomainName}.homestocompare.com`
  // let devDataApiBase = 'https://medo.homestocompare.com'

  let dataApiBase = `${process.env.API_BASE}` || devDataApiBase // 'http://lvh.me:3333'

  if (process.env.PROD) {
    dataApiBase = prodDataApiBase
    //  localOverRide === 'local' ? devDataApiBase : prodDataApiBase
  } else {
    dataApiBase = devDataApiBase
    //  localOverRide === 'prod' ? prodDataApiBase : devDataApiBase
  }

  // let reflectionsApiBase = `${dataApiBase}/gipety`
  // if (subdomainSlug && subdomainSlug.length > 2) {
  //   // subdomainSlug is optional
  //   reflectionsApiBase = `${dataApiBase}/${subdomainSlug}`
  // }
  // if (optionalPackageCode && optionalPackageCode.length > 2) {
  //   // optionalPackageCode is optional
  //   reflectionsApiBase = `${reflectionsApiBase}/api_dee/${optionalPackageCode}/v1`
  // } else {
  //   reflectionsApiBase = `${reflectionsApiBase}/api_dee/v1`
  // }

  pwbFlexConfig = {
    dataApiBase: dataApiBase,
    subdomainName: subdomainName,
  }
  // Add the subdomainEnv to the global properties so it can be accessed anywhere in the app
  app.config.globalProperties.$pwbFlexConfig = pwbFlexConfig
  if (process.env.SERVER) {
    console.log('pwbFlexConfig:', pwbFlexConfig)
  }
  app.provide('pwbFlexConfig', pwbFlexConfig)
  // let flexiConfig = {
  //   lastUpdateDate: 'September 25th, 2024',
  //   serviceEmail: '<EMAIL>',
  //   whitelabelNameDisplay: 'Gipety',
  // }
  // app.config.globalProperties.$flexiConfig = flexiConfig
}

export { pwbFlexConfig }
