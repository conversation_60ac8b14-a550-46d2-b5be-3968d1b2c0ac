import { boot } from 'quasar/wrappers'

// Boot file to load Tawk.to chat widget on client only, controllable via env flags
export default boot(async () => {
  // Do nothing during SSR
  if (process.env.SERVER) return

  const enabled = (process.env.TAWK_TO_ENABLED || 'false').toString().toLowerCase() === 'true'
  if (!enabled) return

  const propertyId = process.env.TAWK_TO_PROPERTY_ID || '689f7f9f8c1f741927d34b82'
  const widgetId = process.env.TAWK_TO_WIDGET_ID || '1j2ngl2d1'

  // Avoid double-inserting the script
  if (document.getElementById('tawkto-script')) return

  const loadTawk = () => {
    // Ensure global objects exist as per Tawk.to docs
    window.Tawk_API = window.Tawk_API || {}
    window.Tawk_LoadStart = new Date()

    const s1 = document.createElement('script')
    s1.id = 'tawkto-script'
    s1.async = true
    s1.src = `https://embed.tawk.to/${propertyId}/${widgetId}`
    s1.charset = 'UTF-8'
    s1.setAttribute('crossorigin', '*')

    const s0 = document.getElementsByTagName('script')[0]
    if (s0 && s0.parentNode) {
      s0.parentNode.insertBefore(s1, s0)
    } else {
      document.head.appendChild(s1)
    }
  }

  // Prefer idle callback, fallback to timeout
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => loadTawk())
  } else {
    setTimeout(() => loadTawk(), 3000)
  }
})

