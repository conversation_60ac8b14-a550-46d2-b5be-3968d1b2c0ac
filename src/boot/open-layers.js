import { boot } from 'quasar/wrappers'
// // import VueGoogleMaps from '@fawmi/vue-google-maps'
// // https://github.com/NathanAP/vue-google-maps-community-fork
// import VueGoogleMaps from 'vue-google-maps-community-fork'
// // import { currentConfigData } from "src/utils/config-data"

// export default boot(({ app }) => {
//   // console.log(`GMAPS_API_KEY1 is ${import.meta.env.GMAPS_API_KEY}`)
//   // // Oct 2024: above returns undefined while below is successful
//   // console.log(`GMAPS_API_KEY2 is ${process.env.GMAPS_API_KEY}`)
//   app.use(VueGoogleMaps, {
//     load: {
//       // key: gak,
//       key: process.env.GMAPS_API_KEY,
//       libraries: 'places',
//     },
//   })
// })

// import { createApp } from 'vue'
// import App from 'src/App.vue'

// Lazy-load OpenLayers only on routes that need it
let olLoaded = false
async function ensureOpenLayers(app) {
  if (olLoaded) return
  const { default: OpenLayersMap } = await import('vue3-openlayers')
  await import('vue3-openlayers/styles.css')
  app.use(OpenLayersMap /*, options */)
  olLoaded = true
}

export default boot(({ app, router }) => {
  // Load for initial route if required
  router.isReady().then(async () => {
    if (router.currentRoute.value?.meta?.usesOpenLayers) {
      await ensureOpenLayers(app)
    }
  })

  // Load on navigation to routes that require OpenLayers
  router.beforeEach(async (to) => {
    if (to.meta?.usesOpenLayers) {
      await ensureOpenLayers(app)
    }
    return true
  })
})
