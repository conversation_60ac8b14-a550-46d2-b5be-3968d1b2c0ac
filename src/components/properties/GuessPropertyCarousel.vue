<template>
  <div class="m-guess-property-carousel" :style="{ height }">
    <q-carousel
      v-model="internalIndex"
      :animated="animated"
      :swipeable="swipeable"
      :arrows="arrows"
      :navigation="navigation"
      :thumbnails="thumbnails"
      :infinite="infinite"
      :height="height"
      class="rounded-borders"
      :control-color="controlColor"
      :control-type="controlType"
    >
      <q-carousel-slide
        v-for="(img, idx) in normalizedImages"
        :key="idx"
        :name="idx"
        :img-src="img"
        class="carousel-slide"
      />

      <template v-if="showNavButtons" #control>
        <q-btn
          class="carousel-nav-btn prev-btn"
          icon="chevron_left"
          round
          unelevated
          color="white"
          text-color="primary"
          size="sm"
          @click="prev"
        />
        <q-btn
          class="carousel-nav-btn next-btn"
          icon="chevron_right"
          round
          unelevated
          color="white"
          text-color="primary"
          size="sm"
          @click="next"
        />
      </template>
    </q-carousel>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'

const props = defineProps({
  images: { type: Array, default: () => [] },
  modelValue: { type: Number, default: 0 },
  height: { type: String, default: '400px' },
  // QCarousel behavior
  animated: { type: Boolean, default: true },
  swipeable: { type: Boolean, default: true },
  arrows: { type: Boolean, default: true },
  navigation: { type: Boolean, default: false },
  thumbnails: { type: Boolean, default: false },
  infinite: { type: Boolean, default: true },
  controlColor: { type: String, default: 'primary' },
  controlType: { type: String, default: 'flat' },
  showNavButtons: { type: Boolean, default: false },
})

const emit = defineEmits(['update:modelValue'])

const internalIndex = ref(props.modelValue)

watch(
  () => props.modelValue,
  (v) => {
    internalIndex.value = v
  }
)

watch(internalIndex, (v) => emit('update:modelValue', v))

const normalizedImages = computed(() =>
  props.images
    .map((img) => {
      if (typeof img === 'string') return img
      return (
        img?.image_details?.url ||
        img?.url ||
        img?.src ||
        img?.full_image_url ||
        ''
      )
    })
    .filter(Boolean)
)

const prev = () => {
  const len = normalizedImages.value.length
  if (len === 0) return
  internalIndex.value = (internalIndex.value - 1 + len) % len
}

const next = () => {
  const len = normalizedImages.value.length
  if (len === 0) return
  internalIndex.value = (internalIndex.value + 1) % len
}
</script>

<style scoped>
.m-guess-property-carousel {
  height: 400px;
}

.carousel-slide {
  background-size: cover;
  background-position: center;
}

@media (max-width: 768px) {
  .m-guess-property-carousel {
    height: 300px;
  }
}
</style>

