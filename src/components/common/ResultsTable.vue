<template>
  <!-- 
    ResultsTable - A reusable component for displaying game results in a table format
    
    Usage example:
    <ResultsTable 
      :rows="gameBreakdown"
      :columns="resultsColumns"
      title="How you performed on each property"
      title-icon="leaderboard"
      :format-guess="formatGuessPrice"
      :format-actual="formatActualPrice"
      :get-score-color="getScoreColor"
      score-display-type="chip"
      property-link-route="rPriceGameProperty"
      :property-link-params="(row) => ({ listingInGameUuid: row.listing_uuid })"
    />
  -->
  <q-card class="results-card q-mb-lg"
          flat
          bordered>
    <q-card-section class="q-pa-lg">
      <div v-if="title" class="text-h6 q-mb-md">
        <q-icon v-if="titleIcon"
                :name="titleIcon"
                color="primary"
                size="sm"
                class="q-mr-sm" />
        {{ title }}
      </div>
      
      <q-table :rows="rows"
               :columns="columns"
               :row-key="rowKey"
               flat
               :pagination="pagination"
               :hide-bottom="hideBottom"
               class="results-table">
        
        <!-- Property column slot -->
        <template v-slot:body-cell-property="props">
          <q-td :props="props">
            <div class="property-cell"
                 style="overflow: auto">
              <router-link v-if="propertyLinkRoute"
                           :to="getPropertyLink(props.row)">
                <div class="text-weight-medium">
                  {{ getPropertyTitle(props.row) }}
                </div>
              </router-link>
              <div v-else class="text-weight-medium">
                {{ getPropertyTitle(props.row) }}
              </div>
              <div class="text-caption text-grey-6">
                {{ getPropertyVicinity(props.row) }}
              </div>
            </div>
          </q-td>
        </template>

        <!-- Guess column slot -->
        <template v-slot:body-cell-guess="props">
          <q-td :props="props">
            <div class="text-weight-medium">
              {{ formatGuess(props.row) }}
            </div>
          </q-td>
        </template>

        <!-- Actual price column slot -->
        <template v-slot:body-cell-actual="props">
          <q-td :props="props">
            <div v-if="getActualPrice(props.row)"
                 class="text-weight-medium">
              {{ formatActual(props.row) }}
            </div>
          </q-td>
        </template>

        <!-- Difference column slot -->
        <template v-slot:body-cell-difference="props">
          <q-td :props="props">
            <q-chip :color="getDifferenceColor(props.row)"
                    text-color="white"
                    dense
                    size="md">
              {{ getDifferenceText(props.row) }}
            </q-chip>
          </q-td>
        </template>

        <!-- Score column slot -->
        <template v-slot:body-cell-score="props">
          <q-td :props="props">
            <div class="score-cell">
              <q-chip v-if="scoreDisplayType === 'chip'"
                      :color="getScoreColor(getScore(props.row))"
                      text-color="white"
                      dense
                      size="md"
                      class="q-pa-sm">
                {{ getScore(props.row) }}
              </q-chip>
              <q-circular-progress v-else
                                   :value="getScore(props.row)"
                                   size="40px"
                                   :thickness="0.15"
                                   :color="getScoreColor(getScore(props.row))"
                                   track-color="grey-3"
                                   class="q-mr-sm">
                <div class="text-caption text-weight-bold">{{ getScore(props.row) }}</div>
              </q-circular-progress>
            </div>
          </q-td>
        </template>
      </q-table>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // Required props
  rows: {
    type: Array,
    required: true
  },
  columns: {
    type: Array,
    required: true
  },
  
  // Display configuration
  title: {
    type: String,
    default: null
  },
  titleIcon: {
    type: String,
    default: 'leaderboard'
  },
  rowKey: {
    type: String,
    default: 'uuid'
  },
  scoreDisplayType: {
    type: String,
    default: 'chip', // 'chip' or 'circular'
    validator: (value) => ['chip', 'circular'].includes(value)
  },
  
  // Table configuration
  pagination: {
    type: Object,
    default: () => ({ rowsPerPage: 0 })
  },
  hideBottom: {
    type: Boolean,
    default: false
  },
  
  // Property link configuration
  propertyLinkRoute: {
    type: String,
    default: null
  },
  propertyLinkParams: {
    type: Function,
    default: null
  },
  
  // Data field mappings - allow customization of field names
  fieldMappings: {
    type: Object,
    default: () => ({
      propertyTitle: 'estimate_title',
      propertyVicinity: 'estimate_vicinity',
      guessedPrice: 'guessed_price_in_ui_currency_cents',
      actualPrice: 'price_at_time_of_estimate_cents',
      currency: 'ui_currency',
      sourceCurrency: 'source_listing_currency',
      percentageDiff: 'percentage_above_or_below',
      score: 'score_for_guess',
      listingUuid: 'listing_uuid'
    })
  },
  
  // Required functions - these must be passed in as they contain business logic
  formatGuess: {
    type: Function,
    required: true
  },
  formatActual: {
    type: Function,
    required: true
  },
  getScoreColor: {
    type: Function,
    required: true
  }
})

// Helper methods for extracting data from rows
const getPropertyTitle = (row) => {
  return row[props.fieldMappings.propertyTitle] || ''
}

const getPropertyVicinity = (row) => {
  return row[props.fieldMappings.propertyVicinity] || ''
}

const getActualPrice = (row) => {
  return row[props.fieldMappings.actualPrice] || row[props.fieldMappings.sourceCurrency]
}

const getScore = (row) => {
  return row[props.fieldMappings.score] || row.estimate_details?.game_score || 0
}

const getDifferenceColor = (row) => {
  const score = getScore(row)
  return props.getScoreColor(score)
}

const getDifferenceText = (row) => {
  const diff = row[props.fieldMappings.percentageDiff]
  if (diff === undefined || diff === null) return '0.0%'
  return `${diff > 0 ? '+' : ''}${diff.toFixed(1)}%`
}

const getPropertyLink = (row) => {
  if (!props.propertyLinkRoute || !props.propertyLinkParams) {
    return '#'
  }
  
  return {
    name: props.propertyLinkRoute,
    params: props.propertyLinkParams(row)
  }
}
</script>

<style scoped>
.results-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.results-table {
  border-radius: 8px;
}

.property-cell {
  max-width: 200px;
}

@media (max-width: 768px) {
  .property-cell {
    max-width: 200px;
  }
}

.score-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
