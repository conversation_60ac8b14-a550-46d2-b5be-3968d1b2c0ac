<template>
  <q-card
    v-if="showLeaderboard && leadingGameScores.length > 0"
    class="leadrbrd-card q-mb-lg q-mt-lg"
    flat
    bordered
  >
    <q-card-section>
      <div class="text-h6 q-mb-md">
        <q-icon
          name="leaderboard"
          color="primary"
          size="sm"
          class="q-mr-sm"
        />
        {{ title }}
      </div>
      <q-table
        :rows="leadingGameScores"
        :columns="columns"
        row-key="uuid"
        flat
        :pagination="pagination"
        :hide-bottom="hideBottom"
        class="leadrbrd-table"
      >
        <template v-slot:body-cell-session_guest_name="props">
          <q-td :props="props">
            <a
              v-if="showLinks && props.row.uuid"
              @click.prevent="navigateToSession(props.row.uuid)"
              href="#"
              class="text-primary text-decoration-none cursor-pointer"
            >
              {{ props.row.session_guest_name }}
            </a>
            <span v-else>
              {{ props.row.session_guest_name }}
            </span>
          </q-td>
        </template>
        <template v-slot:body-cell-performance_percentage="props">
          <q-td :props="props">
            {{ props.row.performance_percentage }}%
          </q-td>
        </template>
        <template v-slot:body-cell-created_at="props">
          <q-td :props="props">
            {{ new Date(props.row.created_at).toLocaleDateString() }}
          </q-td>
        </template>
      </q-table>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const props = defineProps({
  leadingGameScores: {
    type: Array,
    default: () => [],
  },
  showLeaderboard: {
    type: Boolean,
    default: true,
  },
  showLinks: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: 'Leaderboard',
  },
  columns: {
    type: Array,
    default: () => [
      {
        name: 'session_guest_name',
        label: 'Player',
        field: 'session_guest_name',
        align: 'left',
      },
      { 
        name: 'total_score', 
        label: 'Score', 
        field: 'total_score', 
        align: 'right' 
      },
      {
        name: 'max_possible_score',
        label: 'Max Score',
        field: 'max_possible_score',
        align: 'right',
      },
      {
        name: 'performance_percentage',
        label: '%',
        field: 'performance_percentage',
        align: 'right',
      },
      { 
        name: 'created_at', 
        label: 'Date', 
        field: 'created_at', 
        align: 'right' 
      },
    ],
  },
  pagination: {
    type: Object,
    default: () => ({ rowsPerPage: 0 }),
  },
  rowKey: {
    type: String,
    default: 'uuid',
  },
})

const hideBottom = computed(() => {
  return props.leadingGameScores.length < 10
})

// Navigate to session results page
const navigateToSession = (sessionUuid) => {
  router.push({
    name: 'rPriceGameResultsDetailedByUuid',
    params: { sessionUuid }
  })
}
</script>

<style scoped>
.leadrbrd-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.leadrbrd-table {
  background: transparent;
}

.leadrbrd-table .text-primary {
  color: #1976d2 !important;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
}

.leadrbrd-table .text-primary:hover {
  text-decoration: underline;
  color: #1565c0 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .leadrbrd-card {
    margin: 0.5rem 0;
  }
}
</style>
