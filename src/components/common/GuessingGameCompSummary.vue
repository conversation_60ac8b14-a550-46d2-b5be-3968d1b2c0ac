<template>
  <!-- 
    GuessingGameCompSummary - A reusable component for displaying comparison summary
    between player guesses and other players' performance
    
    Usage example:
    <GuessingGameCompSummary 
      :comparison-data="comparisonSummary"
      :is-current-user-session="true"
      player-name="John"
      :show-prices="true"
      :is-loading="false"
      :format-price="formatPrice"
    />
  -->
  <q-card v-if="comparisonData.length > 0 || isLoading"
          class="comparison-card q-mb-lg"
          flat
          bordered>
    <q-card-section class="q-pa-lg">
      <div class="text-h6 q-mb-md">
        <q-icon name="people"
                color="primary"
                size="sm"
                class="q-mr-sm" />
        {{ getTitle() }}
      </div>

      <!-- Loading State -->
      <div v-if="isLoading"
           class="text-center q-pa-md">
        <q-spinner color="primary"
                   size="2em" />
        <div class="q-mt-sm text-grey-7">{{ loadingText }}</div>
      </div>

      <!-- Comparison Data -->
      <div v-else>
        <div v-for="(propertyData, index) in comparisonData"
             :key="getPropertyKey(propertyData, index)"
             class="property-comparison q-mb-lg">
          
          <!-- Property Header -->
          <div class="comparison-header q-mb-md">
            <div v-if="propertyData.property_url"
                 class="text-subtitle1 text-weight-medium">
              <a :href="propertyData.property_url">
                {{ getPropertyTitle(propertyData) }}
              </a>
            </div>
            <div v-else
                 class="text-subtitle1 text-weight-medium">
              {{ getPropertyTitle(propertyData) }}
            </div>
            <div class="text-caption text-grey-6">
              {{ getPropertyVicinity(propertyData) }}
            </div>
          </div>

          <!-- No data available message -->
          <div v-if="!hasValidData(propertyData)"
               class="text-grey-6 q-pa-md text-center">
            <q-icon name="info"
                    class="q-mr-sm" />
            {{ noDataMessage }}
          </div>

          <!-- Price comparison stats - only show if we have data and should show prices -->
          <div v-else-if="showPrices"
               class="comparison-stats q-mb-md">
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-4">
                <q-card flat
                        bordered
                        class="stat-card">
                  <q-card-section class="text-center q-pa-md">
                    <div class="text-h6 text-primary">
                      {{ getYourGuessFormatted(propertyData) }}
                    </div>
                    <div class="text-caption text-grey-6">Your Guess</div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col-12 col-md-4">
                <q-card flat
                        bordered
                        class="stat-card">
                  <q-card-section class="text-center q-pa-md">
                    <div class="text-h6 text-secondary">
                      {{ getAverageGuessFormatted(propertyData) }}
                    </div>
                    <div class="text-caption text-grey-6">Average Guess</div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col-12 col-md-4">
                <q-card flat
                        bordered
                        class="stat-card">
                  <q-card-section class="text-center q-pa-md">
                    <div class="text-h6 text-positive">
                      {{ getActualPriceFormatted(propertyData) }}
                    </div>
                    <div class="text-caption text-grey-6">Actual Price</div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>

          <!-- Performance ranking -->
          <div v-if="hasValidData(propertyData)"
               class="performance-ranking">
            <div class="text-subtitle2 q-mb-sm">
              {{ getPerformanceTitle() }}
            </div>
            <div class="ranking-info">
              <q-chip :color="getRankingColor(propertyData)"
                      text-color="white"
                      icon="emoji_events">
                Ranked {{ getRank(propertyData) }} of {{ getTotalPlayers(propertyData) }}
              </q-chip>
              <span class="q-ml-md text-body2 text-grey-7">
                {{ getPerformanceText(propertyData) }}
              </span>
            </div>
          </div>

          <!-- Separator between properties -->
          <q-separator v-if="index < comparisonData.length - 1"
                       class="q-mt-lg" />
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup>
const props = defineProps({
  // Required props
  comparisonData: {
    type: Array,
    required: true
  },
  
  // Display configuration
  isCurrentUserSession: {
    type: Boolean,
    default: true
  },
  playerName: {
    type: String,
    default: ''
  },
  showPrices: {
    type: Boolean,
    default: true
  },
  
  // Loading state
  isLoading: {
    type: Boolean,
    default: false
  },
  loadingText: {
    type: String,
    default: 'Loading comparison data...'
  },
  
  // Customizable messages
  noDataMessage: {
    type: String,
    default: 'No other estimates available for this property yet'
  },
  
  // Custom title override
  customTitle: {
    type: String,
    default: null
  },
  
  // Field mapping configuration
  fieldMappings: {
    type: Object,
    default: () => ({
      propertyTitle: 'property_title',
      propertyVicinity: 'property_vicinity',
      yourGuessFormatted: 'your_guess_formatted',
      averageGuessFormatted: 'average_guess_formatted',
      actualPriceFormatted: 'actual_price_formatted',
      yourGuess: 'your_guess',
      averageGuess: 'average_guess',
      actualPrice: 'actual_price',
      currency: 'currency',
      rankingColor: 'ranking.color',
      rank: 'ranking.rank',
      totalPlayers: 'ranking.total_players',
      performanceText: 'ranking.performance_text',
      priceEstimatesSummary: 'price_estimates_summary'
    })
  },
  
  // Optional formatting function for prices
  formatPrice: {
    type: Function,
    default: null
  }
})

// Helper function to get nested property values
const getNestedValue = (obj, path) => {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

// Title generation
const getTitle = () => {
  if (props.customTitle) return props.customTitle
  
  if (props.isCurrentUserSession) {
    return 'How You Compare to Other Players'
  } else {
    return `How ${props.playerName} Compares to Other Players`
  }
}

const getPerformanceTitle = () => {
  if (props.isCurrentUserSession) {
    return 'Your Performance'
  } else {
    return `${props.playerName}'s Performance`
  }
}

// Property information getters
const getPropertyKey = (propertyData, index) => {
  return propertyData.id || propertyData.uuid || index
}

const getPropertyTitle = (propertyData) => {
  return getNestedValue(propertyData, props.fieldMappings.propertyTitle) || ''
}

const getPropertyVicinity = (propertyData) => {
  return getNestedValue(propertyData, props.fieldMappings.propertyVicinity) || ''
}

// Data validation
const hasValidData = (propertyData) => {
  // Check if we have price estimates summary (for some components)
  const priceEstimates = getNestedValue(propertyData, props.fieldMappings.priceEstimatesSummary)
  if (priceEstimates !== undefined) {
    return Array.isArray(priceEstimates) && priceEstimates.length > 0
  }
  
  // Otherwise, check if we have basic ranking data
  const rank = getRank(propertyData)
  return rank !== null && rank !== undefined
}

// Price formatting getters
const getYourGuessFormatted = (propertyData) => {
  const formatted = getNestedValue(propertyData, props.fieldMappings.yourGuessFormatted)
  if (formatted) return formatted
  
  // Fallback to formatPrice function if available
  if (props.formatPrice) {
    const guess = getNestedValue(propertyData, props.fieldMappings.yourGuess)
    const currency = getNestedValue(propertyData, props.fieldMappings.currency)
    if (guess !== undefined && currency) {
      return props.formatPrice(guess, currency)
    }
  }
  
  return ''
}

const getAverageGuessFormatted = (propertyData) => {
  const formatted = getNestedValue(propertyData, props.fieldMappings.averageGuessFormatted)
  if (formatted) return formatted
  
  // Fallback to formatPrice function if available
  if (props.formatPrice) {
    const guess = getNestedValue(propertyData, props.fieldMappings.averageGuess)
    const currency = getNestedValue(propertyData, props.fieldMappings.currency)
    if (guess !== undefined && currency) {
      return props.formatPrice(guess, currency)
    }
  }
  
  return ''
}

const getActualPriceFormatted = (propertyData) => {
  const formatted = getNestedValue(propertyData, props.fieldMappings.actualPriceFormatted)
  if (formatted) return formatted
  
  // Fallback to formatPrice function if available
  if (props.formatPrice) {
    const price = getNestedValue(propertyData, props.fieldMappings.actualPrice)
    const currency = getNestedValue(propertyData, props.fieldMappings.currency)
    if (price !== undefined && currency) {
      return props.formatPrice(price, currency)
    }
  }
  
  return ''
}

// Ranking getters
const getRankingColor = (propertyData) => {
  return getNestedValue(propertyData, props.fieldMappings.rankingColor) || 
         propertyData.ranking_color || 
         'primary'
}

const getRank = (propertyData) => {
  return getNestedValue(propertyData, props.fieldMappings.rank) || 
         propertyData.ranking
}

const getTotalPlayers = (propertyData) => {
  return getNestedValue(propertyData, props.fieldMappings.totalPlayers) || 
         propertyData.total_players
}

const getPerformanceText = (propertyData) => {
  return getNestedValue(propertyData, props.fieldMappings.performanceText) || 
         propertyData.performance_text || 
         ''
}
</script>

<style scoped>
.comparison-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.comparison-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 0.5rem;
}

.stat-card {
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.performance-ranking {
  border-radius: 8px;
  padding: 1rem;
}

.ranking-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.property-comparison {
  border-left: 4px solid #e0e0e0;
  padding-left: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .comparison-stats .row {
    flex-direction: column;
  }

  .ranking-info {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
