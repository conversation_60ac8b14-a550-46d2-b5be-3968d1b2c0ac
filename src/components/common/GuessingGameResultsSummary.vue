<template>
  <!-- 
    GuessingGameResultsSummary - A comprehensive component showing comparison summary
    between player guesses and other players' performance, including scores and accuracy
    
    Usage example:
    <GuessingGameResultsSummary 
      :rows="gameBreakdown"
      :comparison-data="comparisonSummary"
      title="Complete Game Results"
      title-icon="leaderboard"
      :format-guess="formatGuessPrice"
      :format-actual="formatActualPrice"
      :get-score-color="getScoreColor"
      :format-price="formatPrice"
      :is-current-user-session="true"
      player-name="John"
      :show-prices="true"
      :show-comparison="true"
      :show-overall-stats="true"
      score-display-type="chip"
      property-link-route="rPriceGameProperty"
      :property-link-params="(row) => ({ listingInGameUuid: row.listing_uuid })"
    />
  -->
  <div class="guessing-game-results-summary">
    <!-- Comparison Summary Section -->
    <q-card
      v-if="
        showComparison && (comparisonData.length > 0 || isComparisonLoading)
      "
      class="comparison-card q-mb-lg"
      flat
      bordered
    >
      <q-card-section class="q-pa-lg">
        <div class="text-h6 q-mb-md">
          <q-icon name="people" color="primary" size="sm" class="q-mr-sm" />
          {{ getComparisonTitle() }}
        </div>

        <!-- Comparison Loading State -->
        <div v-if="isComparisonLoading" class="text-center q-pa-md">
          <q-spinner color="primary" size="2em" />
          <div class="q-mt-sm text-grey-7">{{ comparisonLoadingText }}</div>
        </div>

        <!-- Comparison Data -->
        <div v-else>
          <div
            v-for="(propertyData, index) in comparisonData"
            :key="getComparisonPropertyKey(propertyData, index)"
            class="property-comparison q-mb-lg"
          >
            <!-- Property Header -->
            <div class="comparison-header q-mb-md">
              <!-- <div v-if="propertyData.property_url" class="feedback-url">
              </div> -->
              <div
                v-if="getPropertyRouteLink(propertyData)"
                class="feedback-internal-link"
              >
                <router-link
                  :to="getPropertyRouteLink(propertyData)"
                  class="text-subtitle1 text-weight-medium"
                >
                  {{ getComparisonPropertyTitle(propertyData) }}
                </router-link>
              </div>
              <div v-if="propertyData.property_url" class="">
                <q-icon name="link" size="xs" class="q-mr-xs" />
                <a
                  :href="propertyData.property_url"
                  target="_blank"
                  class="text-primary"
                >
                  View original listing
                  <q-icon name="open_in_new" size="xs" class="q-ml-xs" />
                </a>
                <!-- <a :href="propertyData.property_url">
                  {{ getComparisonPropertyTitle(propertyData) }}
                </a> -->
              </div>
              <!-- <div v-else class="text-subtitle1 text-weight-medium">
                {{ getComparisonPropertyTitle(propertyData) }}
              </div> -->
              <div class="text-caption text-grey-6">
                {{ getComparisonPropertyVicinity(propertyData) }}
              </div>
            </div>

            <!-- No data available message -->
            <div
              v-if="!hasValidComparisonData(propertyData)"
              class="text-grey-6 q-pa-md text-center"
            >
              <q-icon name="info" class="q-mr-sm" />
              {{ noDataMessage }}
            </div>

            <!-- Price comparison stats with score and difference -->
            <div v-else-if="showPrices" class="comparison-stats q-mb-md">
              <div class="row q-col-gutter-md">
                <div class="col-12 col-md-3">
                  <q-card flat bordered class="stat-card">
                    <q-card-section class="text-center q-pa-md">
                      <div class="text-h6 text-primary">
                        {{ getYourGuessFormatted(propertyData) }}
                      </div>
                      <div class="text-caption text-grey-6">Your Guess</div>
                    </q-card-section>
                  </q-card>
                </div>
                <div class="col-12 col-md-3">
                  <q-card flat bordered class="stat-card">
                    <q-card-section class="text-center q-pa-md">
                      <div class="text-h6 text-positive">
                        {{ getActualPriceFormatted(propertyData) }}
                      </div>
                      <div class="text-caption text-grey-6">Actual Price</div>
                    </q-card-section>
                  </q-card>
                </div>
                <div class="col-12 col-md-3">
                  <q-card flat bordered class="stat-card">
                    <q-card-section class="text-center q-pa-md">
                      <div class="q-mb-xs">
                        <q-chip
                          :color="getComparisonScoreColor(propertyData)"
                          text-color="white"
                          dense
                          size="md"
                          class="q-pa-sm"
                        >
                          {{ getComparisonScore(propertyData) }}
                        </q-chip>
                      </div>
                      <div class="text-caption text-grey-6">Score</div>
                    </q-card-section>
                  </q-card>
                </div>
                <div class="col-12 col-md-3">
                  <q-card flat bordered class="stat-card">
                    <q-card-section class="text-center q-pa-md">
                      <div class="text-h6 text-secondary">
                        {{ getAverageGuessFormatted(propertyData) }}
                      </div>
                      <div class="text-caption text-grey-6">Average Guess</div>
                    </q-card-section>
                  </q-card>
                </div>
              </div>

              <!-- Difference/Accuracy Row -->
              <div class="row q-col-gutter-md q-mt-sm">
                <div class="col-12">
                  <q-card flat bordered class="stat-card">
                    <q-card-section class="text-center q-pa-md">
                      <div class="q-mb-xs">
                        <q-chip
                          :color="getComparisonDifferenceColor(propertyData)"
                          text-color="white"
                          dense
                          size="md"
                        >
                          {{ getComparisonDifferenceText(propertyData) }}
                        </q-chip>
                      </div>
                      <div class="text-caption text-grey-6">
                        Accuracy (Difference from Actual)
                      </div>
                    </q-card-section>
                  </q-card>
                </div>
              </div>
            </div>

            <!-- Performance ranking -->
            <div
              v-if="hasValidComparisonData(propertyData)"
              class="performance-ranking"
            >
              <div class="text-subtitle2 q-mb-sm">
                {{ getPerformanceTitle() }}
              </div>
              <div class="ranking-info">
                <q-chip
                  :color="getRankingColor(propertyData)"
                  text-color="white"
                  icon="emoji_events"
                >
                  Ranked {{ getRank(propertyData) }} of
                  {{ getTotalPlayers(propertyData) }}
                </q-chip>
                <span class="q-ml-md text-body2 text-grey-7">
                  {{ getPerformanceText(propertyData) }}
                </span>
              </div>
            </div>

            <!-- Property Feedback Section -->
            <div
              v-if="hasPropertyFeedback(propertyData)"
              class="property-feedback q-mt-md"
            >
              <div class="text-subtitle2 q-mb-sm">
                <q-icon
                  name="feedback"
                  color="primary"
                  size="sm"
                  class="q-mr-sm"
                />
                {{ getFeedbackTitle() }}
              </div>
              <q-card flat bordered class="feedback-card">
                <q-card-section class="q-pa-md">
                  <div class="feedback-text text-body1 q-mb-sm">
                    "{{ getPropertyFeedbackText(propertyData) }}"
                  </div>
                  <div class="feedback-meta text-caption text-grey-6">
                    <div class="feedback-date q-mb-xs">
                      <q-icon name="schedule" size="xs" class="q-mr-xs" />
                      Submitted {{ getFormattedFeedbackDate(propertyData) }}
                    </div>
                  </div>
                </q-card-section>
              </q-card>
            </div>

            <!-- Separator between properties -->
            <q-separator
              v-if="index < comparisonData.length - 1"
              class="q-mt-lg"
            />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Overall Summary Stats -->
    <q-card
      v-if="showOverallStats && (rows.length > 0 || comparisonData.length > 0)"
      class="overall-stats-card"
      flat
      bordered
    >
      <q-card-section class="q-pa-lg">
        <div class="text-h6 q-mb-md">
          <q-icon name="analytics" color="primary" size="sm" class="q-mr-sm" />
          Overall Performance Summary
        </div>

        <div class="row q-col-gutter-md">
          <div class="col-6 col-md-3">
            <div class="text-center">
              <div class="text-h5 text-secondary">{{ getAverageScore() }}</div>
              <div class="text-caption text-grey-6">Average Score</div>
            </div>
          </div>
          <div class="col-6 col-md-3">
            <div class="text-center">
              <div class="text-h5 text-positive">{{ getBestScore() }}</div>
              <div class="text-caption text-grey-6">Best Score</div>
            </div>
          </div>
          <div class="col-6 col-md-3">
            <div class="text-center">
              <div class="text-h5 text-info">{{ getAverageAccuracy() }}</div>
              <div class="text-caption text-grey-6">Avg Accuracy</div>
            </div>
          </div>
          <div class="col-6 col-md-3">
            <div class="text-center">
              <div class="text-h5 text-primary">{{ getTotalProperties() }}</div>
              <div class="text-caption text-grey-6">Properties</div>
            </div>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // Results Data Props (kept for overall stats and fallback data)
  rows: {
    type: Array,
    default: () => [],
  },

  // Comparison Data Props
  comparisonData: {
    type: Array,
    default: () => [],
  },

  // Display configuration
  title: {
    type: String,
    default: 'Game Results & Performance',
  },
  // titleIcon: {
  //   type: String,
  //   default: 'leaderboard',
  // },
  scoreDisplayType: {
    type: String,
    default: 'chip',
    validator: (value) => ['chip', 'circular'].includes(value),
  },

  // Section visibility
  showComparison: {
    type: Boolean,
    default: true,
  },
  showOverallStats: {
    type: Boolean,
    default: true,
  },
  showPrices: {
    type: Boolean,
    default: true,
  },

  // Player information
  isCurrentUserSession: {
    type: Boolean,
    default: true,
  },
  playerName: {
    type: String,
    default: '',
  },

  // Loading states
  isComparisonLoading: {
    type: Boolean,
    default: false,
  },
  comparisonLoadingText: {
    type: String,
    default: 'Loading comparison data...',
  },

  // Property link configuration
  propertyLinkRoute: {
    type: String,
    default: null,
  },
  propertyLinkParams: {
    type: Function,
    default: null,
  },

  // Customizable messages
  noDataMessage: {
    type: String,
    default: 'No other estimates available for this property yet',
  },
  customComparisonTitle: {
    type: String,
    default: null,
  },

  // Data field mappings for fallback table data
  tableFieldMappings: {
    type: Object,
    default: () => ({
      propertyTitle: 'estimate_title',
      propertyVicinity: 'estimate_vicinity',
      guessedPrice: 'guessed_price_in_ui_currency_cents',
      actualPrice: 'price_at_time_of_estimate_cents',
      currency: 'ui_currency',
      sourceCurrency: 'source_listing_currency',
      percentageDiff: 'percentage_above_or_below',
      score: 'score_for_guess',
      listingUuid: 'listing_uuid',
    }),
  },

  // Field mapping configuration for comparison
  comparisonFieldMappings: {
    type: Object,
    default: () => ({
      propertyTitle: 'property_title',
      propertyVicinity: 'property_vicinity',
      yourGuessFormatted: 'your_guess_formatted',
      averageGuessFormatted: 'average_guess_formatted',
      actualPriceFormatted: 'actual_price_formatted',
      yourGuess: 'your_guess',
      averageGuess: 'average_guess',
      actualPrice: 'actual_price',
      currency: 'currency',
      rankingColor: 'ranking.color',
      rank: 'ranking.rank',
      totalPlayers: 'ranking.total_players',
      performanceText: 'ranking.performance_text',
      priceEstimatesSummary: 'price_estimates_summary',
    }),
  },

  // Required formatting functions
  formatGuess: {
    type: Function,
    required: true,
  },
  formatActual: {
    type: Function,
    required: true,
  },
  getScoreColor: {
    type: Function,
    required: true,
  },
  formatPrice: {
    type: Function,
    default: null,
  },
})

// Helper function to get nested property values
const getNestedValue = (obj, path) => {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}

// ===== TABLE-RELATED METHODS =====

// Helper methods for extracting data from table rows
const getPropertyTitle = (row) => {
  return row[props.tableFieldMappings.propertyTitle] || ''
}

const getPropertyVicinity = (row) => {
  return row[props.tableFieldMappings.propertyVicinity] || ''
}

const getActualPrice = (row) => {
  return (
    row[props.tableFieldMappings.actualPrice] ||
    row[props.tableFieldMappings.sourceCurrency]
  )
}

const getScore = (row) => {
  return (
    row[props.tableFieldMappings.score] || row.estimate_details?.game_score || 0
  )
}

const getDifferenceColor = (row) => {
  const score = getScore(row)
  return props.getScoreColor(score)
}

const getDifferenceText = (row) => {
  const diff = row[props.tableFieldMappings.percentageDiff]
  if (diff === undefined || diff === null) return '0.0%'
  return `${diff > 0 ? '+' : ''}${diff.toFixed(1)}%`
}

const getPropertyLink = (row) => {
  if (!props.propertyLinkRoute || !props.propertyLinkParams) {
    return '#'
  }

  return {
    name: props.propertyLinkRoute,
    params: props.propertyLinkParams(row),
  }
}

// ===== COMPARISON-RELATED METHODS =====

// Title generation
const getComparisonTitle = () => {
  if (props.customComparisonTitle) return props.customComparisonTitle

  if (props.isCurrentUserSession) {
    return 'How You Performed in Detail'
  } else {
    return `How ${props.playerName} performed in detail`
  }
}

const getPerformanceTitle = () => {
  if (props.isCurrentUserSession) {
    return 'Your Performance'
  } else {
    return `${props.playerName}'s Performance`
  }
}

const getFeedbackTitle = () => {
  if (props.isCurrentUserSession) {
    return 'Your Thoughts on This Property'
  } else {
    return `${props.playerName}'s Thoughts on This Property`
  }
}

// Property information getters for comparison
const getComparisonPropertyKey = (propertyData, index) => {
  return propertyData.id || propertyData.uuid || index
}

const getComparisonPropertyTitle = (propertyData) => {
  return (
    getNestedValue(propertyData, props.comparisonFieldMappings.propertyTitle) ||
    ''
  )
}

const getComparisonPropertyVicinity = (propertyData) => {
  return (
    getNestedValue(
      propertyData,
      props.comparisonFieldMappings.propertyVicinity
    ) || ''
  )
}

// Data validation for comparison
const hasValidComparisonData = (propertyData) => {
  const priceEstimates = getNestedValue(
    propertyData,
    props.comparisonFieldMappings.priceEstimatesSummary
  )
  if (priceEstimates !== undefined) {
    return Array.isArray(priceEstimates) && priceEstimates.length > 0
  }

  const rank = getRank(propertyData)
  return rank !== null && rank !== undefined
}

// Price formatting getters for comparison
const getYourGuessFormatted = (propertyData) => {
  const formatted = getNestedValue(
    propertyData,
    props.comparisonFieldMappings.yourGuessFormatted
  )
  if (formatted) return formatted

  if (props.formatPrice) {
    const guess = getNestedValue(
      propertyData,
      props.comparisonFieldMappings.yourGuess
    )
    const currency = getNestedValue(
      propertyData,
      props.comparisonFieldMappings.currency
    )
    if (guess !== undefined && currency) {
      return props.formatPrice(guess, currency)
    }
  }

  return ''
}

const getAverageGuessFormatted = (propertyData) => {
  const formatted = getNestedValue(
    propertyData,
    props.comparisonFieldMappings.averageGuessFormatted
  )
  if (formatted) return formatted

  if (props.formatPrice) {
    const guess = getNestedValue(
      propertyData,
      props.comparisonFieldMappings.averageGuess
    )
    const currency = getNestedValue(
      propertyData,
      props.comparisonFieldMappings.currency
    )
    if (guess !== undefined && currency) {
      return props.formatPrice(guess, currency)
    }
  }

  return ''
}

const getActualPriceFormatted = (propertyData) => {
  const formatted = getNestedValue(
    propertyData,
    props.comparisonFieldMappings.actualPriceFormatted
  )
  if (formatted) return formatted

  if (props.formatPrice) {
    const price = getNestedValue(
      propertyData,
      props.comparisonFieldMappings.actualPrice
    )
    const currency = getNestedValue(
      propertyData,
      props.comparisonFieldMappings.currency
    )
    if (price !== undefined && currency) {
      return props.formatPrice(price, currency)
    }
  }

  return ''
}

// Ranking getters for comparison
const getRankingColor = (propertyData) => {
  return (
    getNestedValue(propertyData, props.comparisonFieldMappings.rankingColor) ||
    propertyData.ranking_color ||
    'primary'
  )
}

const getRank = (propertyData) => {
  return (
    getNestedValue(propertyData, props.comparisonFieldMappings.rank) ||
    propertyData.ranking
  )
}

const getTotalPlayers = (propertyData) => {
  return (
    getNestedValue(propertyData, props.comparisonFieldMappings.totalPlayers) ||
    propertyData.total_players
  )
}

const getPerformanceText = (propertyData) => {
  return (
    getNestedValue(
      propertyData,
      props.comparisonFieldMappings.performanceText
    ) ||
    propertyData.performance_text ||
    ''
  )
}

// Property feedback methods
const hasPropertyFeedback = (propertyData) => {
  const feedback = propertyData.property_feedback
  return (
    feedback &&
    feedback.feedback_text &&
    feedback.feedback_text.trim().length > 0
  )
}

const getPropertyFeedbackText = (propertyData) => {
  return propertyData.property_feedback?.feedback_text || ''
}

const getFormattedFeedbackDate = (propertyData) => {
  const submittedAt = propertyData.property_feedback?.submitted_at
  if (!submittedAt) return ''

  try {
    const date = new Date(submittedAt)
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch (error) {
    return ''
  }
}

const getPropertyRouteLink = (propertyData) => {
  if (!props.propertyLinkRoute || !props.propertyLinkParams) {
    return null
  }

  try {
    // Debug: Log the property data to understand its structure
    // console.log('Property data for route generation:', propertyData)

    // Try to find the listing UUID in various possible fields
    let listingUuid = propertyData.listing_in_game_uuid

    //   propertyData.listing_uuid ||
    //   propertyData.uuid ||
    //   propertyData.listingInGameUuid ||
    //   propertyData.listing_in_game_uuid ||
    //   propertyData.id
    // // If no direct UUID found, try to extract from property_url
    // if (!listingUuid && propertyData.property_url) {
    //   const urlMatch = propertyData.property_url.match(/\/details\/(\d+)/)
    //   if (urlMatch) {
    //     listingUuid = urlMatch[1]
    //     console.log('Extracted UUID from URL:', listingUuid)
    //   }
    // }

    if (!listingUuid) {
      console.warn(
        'No listing UUID found in property data for route generation. Available keys:',
        Object.keys(propertyData)
      )
      return null
    }

    // console.log('Found listing UUID:', listingUuid)

    // Create a mock row object from comparison data for compatibility with propertyLinkParams
    const mockRow = {
      [props.tableFieldMappings.listingUuid]: listingUuid,
      listing_uuid: listingUuid, // Also provide direct field in case the mapping doesn't match
      listingInGameUuid: listingUuid, // Common parameter name
      [props.tableFieldMappings.propertyTitle]:
        getComparisonPropertyTitle(propertyData),
      [props.tableFieldMappings.propertyVicinity]:
        getComparisonPropertyVicinity(propertyData),
    }

    // console.log('Mock row for params:', mockRow)

    const params = props.propertyLinkParams(mockRow)
    // console.log('Generated params:', params)

    if (params && Object.keys(params).length > 0) {
      const route = {
        name: props.propertyLinkRoute,
        params: params,
      }
      // console.log('Generated route:', route)
      return route
    }
  } catch (error) {
    console.warn('Error generating property route link:', error)
  }

  return null
}

// Score and difference methods for comparison data
const getComparisonScore = (propertyData) => {
  // Try to get score from comparison data first
  const score = propertyData.score || propertyData.your_score
  if (score !== undefined) return score

  // Fallback to finding matching row data if rows are provided
  if (props.rows.length > 0) {
    const matchingRow = findMatchingRow(propertyData)
    if (matchingRow) return getScore(matchingRow)
  }

  return 0
}

const getComparisonScoreColor = (propertyData) => {
  const score = getComparisonScore(propertyData)
  return props.getScoreColor(score)
}

const getComparisonDifferenceText = (propertyData) => {
  // Try to get difference from comparison data first
  const diff =
    propertyData.percentage_difference ||
    propertyData.your_percentage_difference
  if (diff !== undefined && diff !== null) {
    return `${diff > 0 ? '+' : ''}${diff.toFixed(1)}%`
  }

  // Fallback to finding matching row data if rows are provided
  if (props.rows.length > 0) {
    const matchingRow = findMatchingRow(propertyData)
    if (matchingRow) return getDifferenceText(matchingRow)
  }

  // Calculate from available price data if possible
  const yourGuess = getNestedValue(
    propertyData,
    props.comparisonFieldMappings.yourGuess
  )
  const actualPrice = getNestedValue(
    propertyData,
    props.comparisonFieldMappings.actualPrice
  )

  if (yourGuess && actualPrice && actualPrice > 0) {
    const diff = ((yourGuess - actualPrice) / actualPrice) * 100
    return `${diff > 0 ? '+' : ''}${diff.toFixed(1)}%`
  }

  return '0.0%'
}

const getComparisonDifferenceColor = (propertyData) => {
  const score = getComparisonScore(propertyData)
  return props.getScoreColor(score)
}

// Helper to find matching row data for a comparison property
const findMatchingRow = (propertyData) => {
  if (!props.rows.length) return null

  // Try to match by various identifiers
  const propertyTitle = getComparisonPropertyTitle(propertyData)
  const propertyVicinity = getComparisonPropertyVicinity(propertyData)

  return props.rows.find((row) => {
    const rowTitle = getPropertyTitle(row)
    const rowVicinity = getPropertyVicinity(row)

    // Match by title and vicinity combination
    return rowTitle === propertyTitle && rowVicinity === propertyVicinity
  })
}

// ===== OVERALL STATS METHODS =====

const getTotalProperties = () => {
  return Math.max(props.rows.length, props.comparisonData.length)
}

const getAverageScore = () => {
  if (props.rows.length === 0) return '0'

  const scores = props.rows
    .map((row) => getScore(row))
    .filter((score) => score > 0)
  if (scores.length === 0) return '0'

  const average = scores.reduce((sum, score) => sum + score, 0) / scores.length
  return Math.round(average).toString()
}

const getBestScore = () => {
  if (props.rows.length === 0) return '0'

  const scores = props.rows
    .map((row) => getScore(row))
    .filter((score) => score > 0)
  if (scores.length === 0) return '0'

  return Math.max(...scores).toString()
}

const getAverageAccuracy = () => {
  if (props.rows.length === 0) return '0%'

  const diffs = props.rows
    .map((row) => {
      const diff = row[props.tableFieldMappings.percentageDiff]
      return diff !== undefined && diff !== null ? Math.abs(diff) : null
    })
    .filter((diff) => diff !== null)

  if (diffs.length === 0) return '0%'

  const avgDiff = diffs.reduce((sum, diff) => sum + diff, 0) / diffs.length
  const accuracy = Math.max(0, 100 - avgDiff)
  return `${accuracy.toFixed(1)}%`
}
</script>

<style scoped>
.guessing-game-results-summary {
  width: 100%;
}

.comparison-card,
.overall-stats-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.comparison-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 0.5rem;
}

.stat-card {
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.performance-ranking {
  border-radius: 8px;
  padding: 1rem;
}

.ranking-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.property-comparison {
  border-left: 4px solid #e0e0e0;
  padding-left: 1rem;
}

.feedback-card {
  border-radius: 8px;
  background: #fafafa;
  border-left: 4px solid #1976d2;
}

.feedback-text {
  font-style: italic;
  line-height: 1.5;
  color: #424242;
}

.feedback-meta {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.feedback-date,
.feedback-url,
.feedback-internal-link {
  display: flex;
  align-items: center;
}

.feedback-url a,
.feedback-internal-link a {
  text-decoration: none;
}

.feedback-url a:hover,
.feedback-internal-link a:hover {
  text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .comparison-stats .row {
    flex-direction: column;
  }

  .ranking-info {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
