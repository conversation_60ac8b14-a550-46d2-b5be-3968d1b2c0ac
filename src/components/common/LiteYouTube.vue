<template>
  <div class="lite-yt" :style="wrapperStyle" ref="wrapper">
    <button
      v-if="!activated"
      class="lite-yt-placeholder"
      type="button"
      :aria-label="`Play video: ${title}`"
      @click="activate"
      @pointerover="warmConnections"
    >
      <img
        class="lite-yt-thumb"
        :src="thumbnailUrl"
        :alt="title"
        loading="lazy"
        decoding="async"
      />
      <div class="lite-yt-playbtn" aria-hidden="true"></div>
    </button>
    <iframe
      v-else
      class="lite-yt-iframe"
      :title="title"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture; web-share"
      allowfullscreen
      loading="lazy"
      referrerpolicy="strict-origin-when-cross-origin"
      :src="iframeSrc"
    ></iframe>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  videoId: { type: String, required: true },
  title: { type: String, default: 'YouTube video' },
  aspectRatio: { type: Number, default: 16 / 9 },
  params: { type: String, default: 'rel=0&modestbranding=1&playsinline=1' },
  autoplayOnClick: { type: Boolean, default: true },
  preloadOnViewport: { type: Boolean, default: true },
})

const activated = ref(false)
const wrapper = ref(null)

const thumbnailUrl = computed(() => `https://i.ytimg.com/vi/${props.videoId}/hqdefault.jpg`)
const wrapperStyle = computed(() => ({
  position: 'relative',
  width: '100%',
  aspectRatio: `${props.aspectRatio}`,
  backgroundColor: '#000',
  overflow: 'hidden',
  borderRadius: '12px'
}))

const iframeSrc = computed(() => {
  const base = `https://www.youtube-nocookie.com/embed/${props.videoId}`
  const autoplay = props.autoplayOnClick ? 'autoplay=1' : ''
  const q = [props.params, autoplay].filter(Boolean).join('&')
  return `${base}?${q}`
})

function activate() {
  activated.value = true
}

let preconnected = false
function warmConnections() {
  if (preconnected) return
  preconnected = true
  try {
    const add = (href) => {
      const link = document.createElement('link')
      link.rel = 'preconnect'
      link.href = href
      link.crossOrigin = ''
      document.head.appendChild(link)
    }
    add('https://www.youtube-nocookie.com')
    add('https://i.ytimg.com')
    add('https://www.google.com')
  } catch (_e) {}
}

onMounted(() => {
  if (!props.preloadOnViewport) return
  if (!('IntersectionObserver' in window)) return
  const el = wrapper.value
  if (!el) return
  const io = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        warmConnections()
        io.disconnect()
      }
    })
  }, { rootMargin: '300px' })
  io.observe(el)
})
</script>

<style scoped>
.lite-yt { position: relative; display: block; }
.lite-yt-placeholder { cursor: pointer; border: 0; padding: 0; margin: 0; width: 100%; height: 100%; background: #000; display: block; position: absolute; inset: 0; }
.lite-yt-thumb { width: 100%; height: 100%; object-fit: cover; display: block; }
.lite-yt-playbtn { width: 68px; height: 48px; background-color: #212121; opacity: 0.8; border-radius: 14%; position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%); }
.lite-yt-playbtn::before { content: ''; border-style: solid; border-width: 11px 0 11px 19px; border-color: transparent transparent transparent #fff; position: absolute; left: 26px; top: 14px; }
.lite-yt-iframe { position: absolute; inset: 0; width: 100%; height: 100%; border: 0; }
</style>
