<template>
  <div class="seo-page q-pa-md">
    <div v-html="html"></div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import content from './seo-content';

const route = useRoute();
const html = ref('');
const pages = import.meta.glob('./content/**/index.html', { as: 'raw' });

async function load(path) {
  const key = `./content${path}/index.html`;
  const meta = content[path] || { title: 'Page', description: '' };
  if (pages[key]) {
    html.value = await pages[key]();
  } else {
    html.value = `<h1>${meta.title}</h1><p>${meta.description}</p>`;
  }
}

load(route.path);
watch(() => route.path, (p) => load(p));
</script>
