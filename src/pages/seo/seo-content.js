export default {
  '/play': {
    title: 'Play the House Price Guessing Game',
    description: 'Central play hub with instant start, featured games, and quick explainer for first-time players.'
  },
  '/play/now': {
    title: 'Play Now: Instant Random Game',
    description: 'One-click start into a randomized multi-round house price game; no signup required.'
  },
  '/play/vs': {
    title: 'Two-Player Head-to-Head',
    description: 'Create a 1v1 room, send an invite link, and battle for the closest guesses.'
  },
  '/play/party': {
    title: 'Party Mode (Up to 10 Players)',
    description: 'Host a group game with a shared lobby, round timers, and live scoreboard—ideal for gatherings.'
  },
  '/play/private-room': {
    title: 'Private Room (Invite-Only)',
    description: 'Spin up a locked game room with unique link and optional passcode for friends or teams.'
  },
  '/play/quick-3': {
    title: '3-Round Quick Game',
    description: 'Fast coffee-break format with three diverse listings, perfect for mobile play.'
  },
  '/play/standard-5': {
    title: '5-Round Standard Game',
    description: 'The default experience with balanced difficulty and pacing.'
  },
  '/play/marathon-10': {
    title: '10-Round Marathon',
    description: 'Long-form session for deep play and higher skill expression.'
  },
  '/play/hints': {
    title: 'Guess with Hints (Hot/Cold)',
    description: 'Assisted mode with proximity feedback and optional clue reveals.'
  },
  '/play/expert': {
    title: 'No-Hints Expert Mode',
    description: 'Pure skill—no clues, tighter timers, and stricter scoring windows.'
  },
  '/play/photo-only': {
    title: 'Photo-Only Challenge',
    description: 'Images only; rely on visual cues to price each property.'
  },
  '/play/flip-reveal': {
    title: 'Flip-Reveal (Card by Card)',
    description: 'Each round reveals extra photos and features only after you lock your guess.'
  },
  '/play/mobile': {
    title: 'Mobile One-Hand Mode',
    description: 'Thumb-friendly layout, large sliders, haptics, and quick actions for phone play.'
  },
  '/play/low-bandwidth': {
    title: 'Low-Bandwidth Mode',
    description: 'Optimized assets and compressed photos for slow or metered connections.'
  },
  '/play/family': {
    title: 'Family-Friendly Filter',
    description: 'Pre-screened listings and language for classrooms and mixed-age play.'
  },
  '/play/accessibility': {
    title: 'Accessibility Mode',
    description: 'High-contrast theme, larger controls, full alt text, and screen-reader cues.'
  },
  '/play/with-friends': {
    title: 'Play With Friends (Share Link)',
    description: 'Generate a game and ready-to-share link in two taps; supports team play.'
  },
  '/play/trending': {
    title: 'Trending Community Games',
    description: 'Auto-refreshed list of most-played community creations—jump in instantly.'
  },
  '/play/new': {
    title: 'New Community Games',
    description: 'Freshly published public games from the community, curated for variety.'
  },
  '/play/picks': {
    title: 'Editor\'s Picks',
    description: 'Hand-curated, high-quality sets highlighting unique markets and property styles.'
  },
  '/play/replays': {
    title: 'Replays & Past Games Archive',
    description: 'Watch or replay finished games with guess distributions and top scores.'
  },
  '/create-game': {
    title: 'Create Your Own Property Price Game',
    description: 'Builder hub to start a new game, preview the flow, and link to all setup steps.'
  },
  '/create-game/getting-started': {
    title: 'Getting Started with the Builder',
    description: 'Step-by-step guide to start a draft, add your first listing, and publish.'
  },
  '/create-game/markets-and-types': {
    title: 'Pick Markets & Property Types',
    description: 'Choose regions and restrict to flats, terraces, semis, detached, condos, or ranch homes.'
  },
  '/create-game/add-listings': {
    title: 'Add Listings (URLs or Upload)',
    description: 'Add individual listing links or upload a file; duplicate detection included.'
  },
  '/create-game/bulk-upload': {
    title: 'Bulk Uploader (CSV/TSV)',
    description: 'Map columns like URL, city, hidden price, and image link to import in one go.'
  },
  '/create-game/round-structure': {
    title: 'Round Structure & Count',
    description: 'Decide number of rounds, per-round listing selection, and ordering.'
  },
  '/create-game/difficulty-and-hints': {
    title: 'Difficulty & Hints',
    description: 'Tune hint rules, tolerance windows, and penalties to adjust challenge.'
  },
  '/create-game/time-limits': {
    title: 'Time Limits & Pacing',
    description: 'Configure per-round timers, intermissions, and countdown behaviors.'
  },
  '/create-game/scoring-options': {
    title: 'Scoring Model Options',
    description: 'Closest-to-price, percentage error, custom bonuses, and tie-breakers.'
  },
  '/create-game/photo-order': {
    title: 'Photo Order & Reveal Rules',
    description: 'Choose cover photo, set reveal sequence, and hide potential spoiler clues.'
  },
  '/create-game/clue-visibility': {
    title: 'Hide/Show Clues (Beds, Baths, Size)',
    description: 'Toggle which facts are visible per round or globally to balance difficulty.'
  },
  '/create-game/randomizer': {
    title: 'Randomizer vs Fixed Order',
    description: 'Shuffle listings for each playthrough or lock a deterministic order for fairness.'
  },
  '/create-game/visibility': {
    title: 'Private vs Public Visibility',
    description: 'Publish privately for invite-only games or list in the community directory.'
  },
  '/create-game/sharing': {
    title: 'Share Links & Invites',
    description: 'Generate share URLs, set link expiry, add passcodes, and export QR codes.'
  },
  '/create-game/lobby': {
    title: 'Lobby & Waiting Room Settings',
    description: 'Enable lobby music, player caps, ready-checks, and late-join rules.'
  },
  '/create-game/branding': {
    title: 'Branding & Theme',
    description: 'Upload logos, pick colors and fonts, and control watermark placement.'
  },
  '/create-game/accessibility': {
    title: 'Accessibility Controls',
    description: 'High-contrast themes, larger controls, alt text review, and keyboard navigation.'
  },
  '/create-game/classroom-mode': {
    title: 'Classroom & Age Filters',
    description: 'One-click classroom presets with safe listings, no chat, and anonymized nicknames.'
  },
  '/create-game/templates': {
    title: 'Save as Template & Packs',
    description: 'Save your configuration as reusable templates or package multi-game packs.'
  },
  '/create-game/troubleshooting': {
    title: 'Debugging & Troubleshooting',
    description: 'Common builder errors, broken link handling, and image fetch fixes.'
  },
  '/daily-challenge': {
    title: 'Daily Challenge Hub',
    description: 'Central landing for daily and weekly play with links to play today and rules.'
  },
  '/daily-challenge/today': {
    title: "Today's Daily Game",
    description: "The current day's challenge with timer, share buttons, and quick how-to." 
  },
  '/daily-challenge/rules': {
    title: 'How the Daily Works (Rules)',
    description: 'Daily-specific rules covering guesses, timing, reveal flow, and tie logic.'
  },
  '/daily-challenge/streaks': {
    title: 'Streaks & Multipliers',
    description: 'How streaks are counted, multipliers for long runs, and streak repair tokens.'
  },
  '/daily-challenge/countdown': {
    title: 'Countdown & Reset Times',
    description: 'Exact reset times by timezone, daily window, and late play FAQ.'
  },
  '/daily-challenge/hard-mode': {
    title: 'Daily Hard Mode',
    description: 'Extra constraints for the daily such as reduced clues and shorter timers.'
  },
  '/daily-challenge/hints': {
    title: 'Daily Hint Schedule',
    description: 'Which clues unlock at which guess thresholds for daily games.'
  },
  '/daily-challenge/share': {
    title: 'Share Results (Spoiler-Safe)',
    description: 'One-tap emoji or graph sharing with spoiler policy and social examples.'
  },
  '/daily-challenge/archive': {
    title: 'Daily Archive Browser',
    description: 'Paginated list of past dailies with dates, difficulty tags, and quick play.'
  },
  '/daily-challenge/yesterday': {
    title: "Yesterday's Answers & Analysis",
    description: 'Full reveal of yesterday’s listings, price deltas, and top community guesses.'
  },
  '/daily-challenge/editions': {
    title: 'Regional Daily Editions',
    description: 'Choose a region-specific daily like UK, US, or EU with localized metrics.'
  },
  '/daily-challenge/uk': {
    title: 'UK Daily Challenge',
    description: 'UK-only daily with EPC, council tax, and property-type nuances.'
  },
  '/daily-challenge/us': {
    title: 'US Daily Challenge',
    description: 'US-only daily with dollars per square foot, HOA notes, and MLS-driven norms.'
  },
  '/weekly-challenge': {
    title: 'Weekly Challenge Hub',
    description: 'Overview of the weekly format with links to the current week’s set.'
  },
  '/weekly-challenge/rules': {
    title: 'Weekly Tournament Rules',
    description: 'Brackets and points format with tie-breakers and team vs solo options.'
  },
  '/weekly-challenge/weekend-mega': {
    title: 'Weekend Mega Round',
    description: 'Special extended round each weekend with bigger point pools.'
  },
  '/weekly-challenge/leaderboards': {
    title: 'Weekly Leaderboards',
    description: 'Weekly-only rankings, prize badges, and season carryover points.'
  },
  '/daily-challenge/subscribe': {
    title: 'Subscribe for Reminders',
    description: 'Opt in for email or push reminders for daily drops and streak alerts.'
  },
  '/daily-challenge/calendar': {
    title: 'Add to Calendar (iCal/Google)',
    description: 'Add daily reset and weekly events to your preferred calendar app.'
  },
  '/daily-challenge/corrections': {
    title: 'Disputes & Corrections (Daily)',
    description: 'Report an incorrect listing or price and see the resolution log.'
  },
  '/uk-house-price-quiz/nationwide': {
    title: 'UK Nationwide Mixed Bag Quiz',
    description: 'Cross-country set mixing cities, suburbs, and rural picks to test broad UK pricing instincts.'
  },
  '/uk-house-price-quiz/london-z1-2-flats': {
    title: 'London Zones 1–2 Flat Prices',
    description: 'Central London flats near Tube hubs—how high is too high?'
  },
  '/uk-house-price-quiz/london-suburbs-semis': {
    title: 'London Suburbs: Semis in Zones 4–6',
    description: 'Family semis in commuter suburbs; spot value versus travel time trade-offs.'
  },
  '/uk-house-price-quiz/manchester-terraces': {
    title: 'Manchester Victorian Terraces',
    description: 'Classic red-brick terraces around Greater Manchester—renovated versus doer-uppers.'
  },
  '/uk-house-price-quiz/birmingham-newbuild': {
    title: 'Birmingham New-Build Estates',
    description: 'Developer-led new builds around the Midlands with varied specs and incentives.'
  },
  '/uk-house-price-quiz/edinburgh-tenements': {
    title: 'Edinburgh Tenement Flats',
    description: 'Stone tenements with period charm; stairs, box rooms, and outlook matter.'
  },
  '/uk-house-price-quiz/glasgow-west-end': {
    title: 'Glasgow West End Flats',
    description: 'Leafy crescents and converted townhouses—premium micro-markets.'
  },
  '/uk-house-price-quiz/cardiff-bay-apartments': {
    title: 'Cardiff Bay Apartments',
    description: 'Waterfront new builds and conversions—amenities versus service charges.'
  },
  '/uk-house-price-quiz/bristol-period-homes': {
    title: 'Bristol Period Homes',
    description: 'Georgian and Victorian gems across Clifton, Redland and surrounds.'
  },
  '/uk-house-price-quiz/leeds-hmo': {
    title: 'Leeds Student Lets (HMO)',
    description: 'Sharer houses near campuses—licensing, yield, and layout quirks.'
  },
  '/uk-house-price-quiz/cornwall-cottages': {
    title: 'Coastal Cornwall Cottages',
    description: 'Holiday-let friendly cottages with sea views, access, and condition premiums.'
  },
  '/uk-house-price-quiz/cotswolds-character': {
    title: 'Cotswolds Character Homes',
    description: 'Honey-stone cottages and barns—kerb appeal versus practical upgrades.'
  },
  '/uk-house-price-quiz/surrey-commuter': {
    title: 'Surrey Commuter Belt Family Homes',
    description: 'Leafy semis and detached homes near fast London links where schools matter.'
  },
  '/uk-house-price-quiz/cambridge-tech-belt': {
    title: 'Cambridge Tech-Belt New-Builds',
    description: 'High-spec homes near science parks; EPC and finishes drive price gaps.'
  },
  '/uk-house-price-quiz/ni-detached-bungalows': {
    title: 'Northern Ireland: Detached & Bungalows',
    description: 'NI market mix outside Belfast—plots, garages, and modernisations.'
  },
  '/uk-house-price-quiz/wales-farmhouses': {
    title: 'Wales Rural Farmhouses',
    description: 'Acreage, outbuildings, and access—renovation scope versus liveability.'
  },
  '/uk-house-price-quiz/scottish-highlands': {
    title: 'Scottish Highlands Lodges',
    description: 'Scenic lodges and cottages—remoteness, land, and tourism premiums.'
  },
  '/uk-house-price-quiz/250k-challenge': {
    title: '£250k Price Band Challenge (UK)',
    description: 'Discover what around £250k buys across the UK, fixer-uppers versus turnkey.'
  },
  '/uk-house-price-quiz/epc-ab': {
    title: 'EPC A–B Energy-Efficient Homes',
    description: 'Low-running-cost homes with insulation, heat pumps, and PV panels.'
  },
  '/uk-house-price-quiz/price-per-sqft': {
    title: 'UK Price-per-Sq-Ft Challenge',
    description: 'Guess using £ per square foot cues—layouts, gardens, and extensions.'
  },
  '/uk-house-price-quiz': {
    title: 'UK House Price Quiz Hub',
    description: 'Country hub targeting UK house price quiz queries with playable UK-focused games.'
  },
  '/us-home-price-quiz': {
    title: 'US Home Price Quiz Hub',
    description: 'Central hub for US-focused quizzes with regional filters and play calls to action.'
  },
  '/us-home-price-quiz/nationwide': {
    title: 'US Nationwide Mixed Bag Quiz',
    description: 'A coast-to-coast sampler to calibrate your US pricing instincts.'
  },
  '/us-home-price-quiz/california-coastal': {
    title: 'California Coastal Homes',
    description: 'From San Diego to Santa Barbara—ocean proximity premiums in action.'
  },
  '/us-home-price-quiz/la-mid-century': {
    title: 'Los Angeles Bungalows & Mid-Century',
    description: 'Post-war bungalows and mid-century gems across LA neighborhoods.'
  },
  '/us-home-price-quiz/sf-condos-victorians': {
    title: 'San Francisco Condos & Victorians',
    description: 'Edwardians and Victorians versus new-build condos—HOA and tech-corridor effects.'
  },
  '/us-home-price-quiz/nyc-coops-condos': {
    title: 'NYC Co-ops & Condos',
    description: 'Manhattan and Brooklyn co-ops vs condos—doorman, amenities, and common charges.'
  },
  '/us-home-price-quiz/brooklyn-brownstones': {
    title: 'Brooklyn Brownstones',
    description: 'Landmark facades, garden levels, and renovation deltas.'
  },
  '/us-home-price-quiz/nj-colonials': {
    title: 'Suburban New Jersey Colonials',
    description: 'Classic 3–4 bed colonials with school, commute, and lot size drivers.'
  },
  '/us-home-price-quiz/texas-suburban': {
    title: 'Texas Suburban Ranch & New Builds',
    description: 'Master-planned communities around DFW, Houston, Austin, and San Antonio.'
  },
  '/us-home-price-quiz/austin-modern-adu': {
    title: 'Austin Moderns & ADUs',
    description: 'Modern infill, ADUs, and walkable pockets shaping price.'
  },
  '/us-home-price-quiz/miami-waterfront': {
    title: 'Miami Waterfront & High-Rise',
    description: 'Waterfront single-family versus condo towers—view tiers and HOA.'
  },
  '/us-home-price-quiz/chicago-greystone': {
    title: 'Chicago Greystones & Two-Flats',
    description: 'Classic two- and three-flats and North Side greystones.'
  },
  '/us-home-price-quiz/seattle-craftsman': {
    title: 'Seattle Craftsman & Townhomes',
    description: 'Craftsman stock versus modern townhomes—transit and tech hubs.'
  },
  '/us-home-price-quiz/phoenix-new-builds': {
    title: 'Phoenix Desert New Builds',
    description: 'New-build stucco homes, pools, and HOA amenities.'
  },
  '/us-home-price-quiz/denver-foothills': {
    title: 'Denver Foothills & Suburbs',
    description: 'Foothill proximity, snow load upgrades, and commuter corridors.'
  },
  '/us-home-price-quiz/boston-triple-deckers': {
    title: 'Boston Triple-Deckers',
    description: 'Classic triple-deckers vs renovated condos—T access premiums.'
  },
  '/us-home-price-quiz/dc-rowhouses': {
    title: 'DC Rowhouses & Condos',
    description: 'Capitol Hill, Shaw, and Petworth rowhouse stock and fee-simple nuances.'
  },
  '/us-home-price-quiz/vegas-new-construction': {
    title: 'Las Vegas New Construction',
    description: 'Spec homes near the Strip versus suburbs; pool and lot premiums.'
  },
  '/us-home-price-quiz/hawaii-islands': {
    title: 'Hawaii Island Homes',
    description: 'O‘ahu, Maui, and Big Island microclimates, STVR rules, and view tiers.'
  },
  '/us-home-price-quiz/500k-challenge': {
    title: '$500k Price Band Challenge (US)',
    description: 'See what around $500k buys across US markets—urban versus suburban contrasts.'
  },
  '/us-home-price-quiz/price-per-sqft': {
    title: 'Price-per-Sq-Ft Challenge (US)',
    description: 'Guess via dollars per square foot cues, finishes, and lot size context.'
  },
  '/city-quizzes': {
    title: 'City House Price Quiz Directory',
    description: 'Master index of city hubs; filter by country, size, or price band.'
  },
  '/city-quizzes/london': {
    title: 'London House Price Quiz Hub',
    description: 'Neighborhood sets from Zones 1–6 with micro-market notes and playable packs.'
  },
  '/city-quizzes/manchester': {
    title: 'Manchester House Price Quiz Hub',
    description: 'Terraces versus new-builds, tram links, and regeneration hotspots.'
  },
  '/city-quizzes/birmingham': {
    title: 'Birmingham House Price Quiz Hub',
    description: 'New-build estates, canalside flats, and commuter belts.'
  },
  '/city-quizzes/edinburgh': {
    title: 'Edinburgh House Price Quiz Hub',
    description: 'Tenements versus townhouses, stair types, box rooms, and outlooks.'
  },
  '/city-quizzes/glasgow': {
    title: 'Glasgow House Price Quiz Hub',
    description: 'West End and Southside contrasts with tenement differentials.'
  },
  '/city-quizzes/bristol': {
    title: 'Bristol House Price Quiz Hub',
    description: 'Period stock in Clifton and Redland versus emerging areas.'
  },
  '/city-quizzes/leeds': {
    title: 'Leeds House Price Quiz Hub',
    description: 'HMOs, student pockets, and northern suburbs.'
  },
  '/city-quizzes/cardiff': {
    title: 'Cardiff House Price Quiz Hub',
    description: 'Bay apartments versus suburbs; transport and amenities.'
  },
  '/city-quizzes/belfast': {
    title: 'Belfast House Price Quiz Hub',
    description: 'Detached and bungalows versus terraces; peace-wall adjacency factors.'
  },
  '/city-quizzes/new-york-city': {
    title: 'New York City House Price Quiz Hub',
    description: 'Borough-by-borough sets with co-op and condo basics and taxes.'
  },
  '/city-quizzes/los-angeles': {
    title: 'Los Angeles House Price Quiz Hub',
    description: 'Valley versus Westside; single-family versus multi-unit comparisons.'
  },
  '/city-quizzes/san-francisco': {
    title: 'San Francisco House Price Quiz Hub',
    description: 'Victorians and Edwardians versus SOMA lofts and tech commute dynamics.'
  },
  '/city-quizzes/chicago': {
    title: 'Chicago House Price Quiz Hub',
    description: 'South and North Side contrasts with two-flats and greystones.'
  },
  '/city-quizzes/miami': {
    title: 'Miami House Price Quiz Hub',
    description: 'Waterfront tiers, flood zones, and condo fees.'
  },
  '/city-quizzes/dallas-fort-worth': {
    title: 'Dallas–Fort Worth House Price Quiz Hub',
    description: 'Master-planned suburbs, school districts, and commute times.'
  },
  '/city-quizzes/seattle': {
    title: 'Seattle House Price Quiz Hub',
    description: 'Craftsman versus townhomes and Link light rail effects.'
  },
  '/city-quizzes/boston': {
    title: 'Boston House Price Quiz Hub',
    description: 'Triple-deckers, brownstones, and MBTA proximity.'
  },
  '/city-quizzes/washington-dc': {
    title: 'Washington DC House Price Quiz Hub',
    description: 'Rowhouses and condos with historic districts and taxation notes.'
  },
  '/city-quizzes/toronto': {
    title: 'Toronto House Price Quiz Hub',
    description: 'Condos versus detached homes with TTC and school catchments.'
  },
  '/city-quizzes/sydney': {
    title: 'Sydney House Price Quiz Hub',
    description: 'Beachside versus inner-west markets, strata fees, and transport.'
  },
  '/rightmove-zoopla-game': {
    title: 'Rightmove & Zoopla Game Setup',
    description: 'Overview of UK listing sources, capabilities, and constraints for building games.'
  },
  '/rightmove-zoopla-game/rightmove-copy-link': {
    title: 'Copying a Rightmove URL (Step-by-Step)',
    description: 'How to grab clean listing links from Rightmove on web or mobile.'
  },
  '/rightmove-zoopla-game/zoopla-copy-link': {
    title: 'Copying a Zoopla URL (Step-by-Step)',
    description: 'How to copy canonical Zoopla URLs without tracking parameters.'
  },
  '/rightmove-zoopla-game/parameter-cleaner': {
    title: 'UK Parameter Cleaner',
    description: 'Auto-strip UTM tracking and session tokens before adding listings to a game.'
  },
  '/rightmove-zoopla-game/poa-handling': {
    title: 'Handling Price-Hidden / POA',
    description: 'What to do when price is hidden or “POA”; fallback clue rules.'
  },
  '/rightmove-zoopla-game/sold-archived': {
    title: 'Sold STC / Archived Listings',
    description: 'Using sold-subject-to-contract or archived pages responsibly.'
  },
  '/rightmove-zoopla-game/image-watermarks': {
    title: 'Image Watermarks & Fair Use (UK)',
    description: 'What images you can show and watermark etiquette.'
  },
  '/rightmove-zoopla-game/preview-errors': {
    title: 'Preview Fetch Errors (UK)',
    description: 'Fix broken previews, blocked images, and 403 responses from portals.'
  },
  '/rightmove-zoopla-game/regional-coverage': {
    title: 'Regional Coverage Notes (UK)',
    description: 'England, Scotland, Wales, and NI quirks and portal coverage differences.'
  },
  '/rightmove-zoopla-game/uk-metrics': {
    title: 'EPC, Council Tax & Metrics',
    description: 'How the game treats EPC bands, council tax, and bed/bath/size fields.'
  },
  '/rightmove-zoopla-game/leasehold-freehold': {
    title: 'Leasehold vs Freehold Hints',
    description: 'Avoiding spoiler-y tenure clues while keeping things fair.'
  },
  '/rightmove-zoopla-game/classroom-safe': {
    title: 'Classroom-Safe UK Sourcing',
    description: 'Filters and presets for school use with UK portals.'
  },
  '/rightmove-zoopla-game/batch-import': {
    title: 'Batch Import from Results Pages',
    description: 'Paste multiple Rightmove or Zoopla links from a search results page.'
  },
  '/rightmove-zoopla-game/deduplication': {
    title: 'De-dup Across Portals (UK)',
    description: 'Detect and merge duplicate listings sourced from both portals.'
  },
  '/rightmove-zoopla-game/attribution': {
    title: 'Attribution & Linking Back (UK)',
    description: 'Required credits and deep-link etiquette for UK sources.'
  },
  '/rightmove-zoopla-game/takedown': {
    title: 'UK Takedown & Reporting',
    description: 'How to flag an issue; DMCA and notice flow with response times.'
  },
  '/rightmove-zoopla-game/template-best-practices': {
    title: 'Template Best Practices (UK)',
    description: 'Building durable UK game templates that won’t break.'
  },
  '/rightmove-zoopla-game/legal': {
    title: 'Legal & Fair-Use Guidance (UK)',
    description: 'Plain-English summary of acceptable use and restrictions.'
  },
  '/rightmove-zoopla-game/accessibility': {
    title: 'Accessibility from UK Listings',
    description: 'Writing alt text and captions based on listing content.'
  },
  '/rightmove-zoopla-game/troubleshooting': {
    title: 'Troubleshooting Checklist (UK)',
    description: 'One-page checklist for common creator pitfalls.'
  },
  '/zillow-realtor-game': {
    title: 'Zillow & Realtor.com Game Setup',
    description: 'Overview of US listing sources, capabilities, and constraints for building games.'
  },
  '/zillow-realtor-game/zillow-copy-link': {
    title: 'Copying a Zillow URL (Step-by-Step)',
    description: 'How to copy clean Zillow links from the web or app.'
  },
  '/zillow-realtor-game/realtor-copy-link': {
    title: 'Copying a Realtor.com URL (Step-by-Step)',
    description: 'Get canonical Realtor.com links without tracking parameters.'
  },
  '/zillow-realtor-game/parameter-cleaner': {
    title: 'US Parameter Cleaner',
    description: 'Strip UTM and session parameters prior to adding links.'
  },
  '/zillow-realtor-game/pending-sold': {
    title: 'Pending/Sold/Off-Market Handling',
    description: 'Rules for using non-active listings in games.'
  },
  '/zillow-realtor-game/price-history-zestimate': {
    title: 'Price History & Zestimate Clues',
    description: 'Manage price history or Zestimate data without spoilers.'
  },
  '/zillow-realtor-game/mls-attribution': {
    title: 'MLS Data & Attribution',
    description: 'Respect MLS attribution rules and image licensing.'
  },
  '/zillow-realtor-game/image-watermarks': {
    title: 'Image Watermarks & Fair Use (US)',
    description: 'What imagery can be displayed and watermark guidance.'
  },
  '/zillow-realtor-game/preview-errors': {
    title: 'Preview Fetch Errors (US)',
    description: 'Fix 403s, lazy-load blocks, and image CDN issues.'
  },
  '/zillow-realtor-game/us-metrics': {
    title: 'US Metrics Mapping ($/Sq Ft, HOA)',
    description: 'Normalize beds, baths, size, HOA dues, lot size, and taxes.'
  },
  '/zillow-realtor-game/hoa-hints': {
    title: 'Condo/HOA Hint Controls',
    description: 'Show or hide HOA dues and amenities to balance difficulty.'
  },
  '/zillow-realtor-game/rentals-vs-sale': {
    title: 'Rentals vs For-Sale Distinction',
    description: 'Keep rental listings out of for-sale games.'
  },
  '/zillow-realtor-game/deduplication': {
    title: 'De-dup Across US Portals',
    description: 'Merge duplicate listings across Zillow, Realtor, and Redfin.'
  },
  '/zillow-realtor-game/state-coverage': {
    title: 'State-by-State Coverage Notes',
    description: 'Portal strengths and quirks by state with disclosure norms.'
  },
  '/zillow-realtor-game/classroom-safe': {
    title: 'Classroom-Safe US Sourcing',
    description: 'Presets for K-12 or college use with US portals.'
  },
  '/zillow-realtor-game/batch-import': {
    title: 'Batch Import from Saved Searches',
    description: 'Paste multiple property links from saved-search exports.'
  },
  '/zillow-realtor-game/takedown': {
    title: 'DMCA/Takedown (US)',
    description: 'Reporting flow for US sources and rights owners.'
  },
  '/zillow-realtor-game/template-best-practices': {
    title: 'Template Best Practices (US)',
    description: 'Building resilient US template packs.'
  },
  '/zillow-realtor-game/accessibility': {
    title: 'Accessibility from US Listings',
    description: 'Crafting alt text and cues from listing content.'
  },
  '/zillow-realtor-game/troubleshooting': {
    title: 'Troubleshooting Checklist (US)',
    description: 'One-pager of common creator fixes.'
  },
  '/game-modes': {
    title: 'Game Modes & Difficulty Levels',
    description: 'Overview of every mode and difficulty toggle with comparisons.'
  },
  '/game-modes/classic': {
    title: 'Classic Mode (Standard Rules)',
    description: 'The default experience—balanced clues, standard timers, and normal scoring.'
  },
  '/game-modes/blitz': {
    title: 'Blitz Mode (Speedrun)',
    description: 'Ultra-fast rounds with minimal clues and aggressive time decay scoring.'
  },
  '/game-modes/zen': {
    title: 'Zen Mode (No Timer)',
    description: 'Relaxed play with no countdown for practice-friendly sessions.'
  },
  '/game-modes/survival': {
    title: 'Survival Mode',
    description: 'Stay alive by staying close; fall outside the window and you’re out.'
  },
  '/game-modes/elimination': {
    title: 'Elimination Brackets',
    description: 'Knockout tournament play through bracketed 1v1 matches.'
  },
  '/game-modes/team-vs-team': {
    title: 'Team vs Team',
    description: 'Cooperative guessing within teams with averaged or captain’s guess.'
  },
  '/game-modes/coop': {
    title: 'Co-op Mode (Shared Guess)',
    description: 'Group agrees on one final number with chat and consensus tools.'
  },
  '/game-modes/draft': {
    title: 'Draft Mode (Creator Picks)',
    description: 'Curators hand-pick rounds with no randomization to showcase sets.'
  },
  '/game-modes/fog-of-info': {
    title: 'Fog-of-Info (Progressive Reveal)',
    description: 'Start with a photo and unlock extra info after locking your guess.'
  },
  '/game-modes/photo-only': {
    title: 'Photo-Only (Text Hidden)',
    description: 'No description or specs—purely visual inference challenge.'
  },
  '/game-modes/text-only': {
    title: 'Text-Only (No Photos)',
    description: 'Descriptions and facts only; evaluate properties without imagery.'
  },
  '/game-modes/blind-band': {
    title: 'Blind Price Band',
    description: 'Guess the price band first, then fine-tune within the revealed range.'
  },
  '/game-modes/double-or-nothing': {
    title: 'Double-or-Nothing Round',
    description: 'Optional risk round to double points or lose them.'
  },
  '/game-modes/time-attack': {
    title: 'Time Attack Ladders',
    description: 'Leaderboards for fastest accurate guesses across micro-rounds.'
  },
  '/game-modes/accessibility': {
    title: 'Accessibility Variant',
    description: 'High-contrast, larger controls, and screen-reader timing accommodations.'
  },
  '/game-modes/classroom-safe': {
    title: 'Classroom Safe Mode',
    description: 'Pre-filtered content, anonymized nicknames, and moderated chat.'
  },
  '/game-modes/regional-lock': {
    title: 'Regional Lock Mode',
    description: 'All rounds constrained to a chosen region or city for fairness.'
  },
  '/game-modes/custom-presets': {
    title: 'Custom Difficulty Presets',
    description: 'Save and share difficulty bundles like timers, hint rules, and error windows.'
  },
  '/game-modes/comparison': {
    title: 'Mode Comparison Matrix',
    description: 'Side-by-side table of rules, timers, hints, and scoring multipliers.'
  },
  '/scoring-leaderboards': {
    title: 'Scoring Rules & Leaderboards',
    description: 'Hub explaining scoring math, ranks, and public boards.'
  },
  '/scoring-leaderboards/absolute-error': {
    title: 'Absolute Error Scoring',
    description: 'Points based on currency error from true price with decay curve examples.'
  },
  '/scoring-leaderboards/percent-error': {
    title: 'Percent Error Scoring',
    description: 'Percentage-off-target model ensuring fairness across price ranges.'
  },
  '/scoring-leaderboards/band-bonuses': {
    title: 'Price-Band Hit Bonuses',
    description: 'Extra points for nailing predefined price bands before the final guess.'
  },
  '/scoring-leaderboards/streaks': {
    title: 'Streak Multipliers',
    description: 'Compounding bonuses for consecutive accurate rounds.'
  },
  '/scoring-leaderboards/tie-breakers': {
    title: 'Tie-Breakers & OT',
    description: 'Nearest-second timestamp, last-round proximity, and sudden-death rules.'
  },
  '/scoring-leaderboards/penalties': {
    title: 'Penalties & Grace Windows',
    description: 'Late locks, AFK, and wild-guess penalties with small-error grace zones.'
  },
  '/scoring-leaderboards/seasons': {
    title: 'Seasonal Ladders',
    description: 'Multi-week seasons, point carryover, and reset policies.'
  },
  '/scoring-leaderboards/tiers': {
    title: 'Divisions & Skill Tiers',
    description: 'Bronze through Master tiers with promotion, relegation, and placement games.'
  },
  '/scoring-leaderboards/private': {
    title: 'Private Leaderboards',
    description: 'Create invite-only boards for friends, classes, or teams.'
  },
  '/scoring-leaderboards/classroom': {
    title: 'Classroom Leaderboards',
    description: 'Rosters, teacher controls, anonymized students, and exportable grades.'
  },
  '/scoring-leaderboards/corporate': {
    title: 'Corporate/Agency Boards',
    description: 'Team dashboards, department filters, and single sign-on access.'
  },
  '/scoring-leaderboards/anti-cheat': {
    title: 'Anti-Cheat & Fair Play',
    description: 'Detection of leaks, duplicate tabs, and collusion with enforcement steps.'
  },
  '/scoring-leaderboards/disputes': {
    title: 'Audit & Dispute Process',
    description: 'How to file disputes, evidence needed, and resolution timelines.'
  },
  '/scoring-leaderboards/stats-glossary': {
    title: 'Stats Glossary',
    description: 'Definitions for mean error, MAPE, RMSE, and other metrics.'
  },
  '/scoring-leaderboards/personal-bests': {
    title: 'Personal Bests & History',
    description: 'Track your records, charts over time, and accuracy by region or mode.'
  },
  '/scoring-leaderboards/badges': {
    title: 'Badges & Achievements',
    description: 'Collectable milestones, streak awards, and event-specific accolades.'
  },
  '/scoring-leaderboards/rewards': {
    title: 'Rewards & Redemptions',
    description: 'Optional prize structures, codes, and sponsor promos with eligibility rules.'
  },
  '/scoring-leaderboards/api': {
    title: 'API: Scores & Webhooks',
    description: 'Export scores and trigger webhooks for results in CSV or JSON formats.'
  },
  '/scoring-leaderboards/privacy': {
    title: 'Data Privacy & Anonymity',
    description: 'What score data is public versus private and nickname policies.'
  },
  '/scoring-leaderboards/regional': {
    title: 'Regional & City Leaderboards',
    description: 'Rankings focused on specific regions or cities for localized bragging rights.'
  },
  '/how-to-guess-house-prices': {
    title: 'How to Guess House Prices',
    description: 'Strategy hub with skill paths for beginners, intermediate, and advanced players.'
  },
  '/how-to-guess-house-prices/exterior-photos': {
    title: 'Reading Exterior Photos',
    description: 'Roof, windows, brick, siding, and landscaping cues to gauge value.'
  },
  '/how-to-guess-house-prices/interior-photos': {
    title: 'Reading Interior Photos',
    description: 'Flooring, kitchens, baths, and fixtures—spotting recent upgrades.'
  },
  '/how-to-guess-house-prices/floor-plans': {
    title: 'Floor Plans & Layout Clues',
    description: 'Flow efficiency, bedroom sizes, and conversion quirks.'
  },
  '/how-to-guess-house-prices/neighborhood-signals': {
    title: 'Neighborhood Signals',
    description: 'Street width, parking, bins, wires, retail mix, and green space indicators.'
  },
  '/how-to-guess-house-prices/transport-premiums': {
    title: 'Transport & Commute Premiums',
    description: 'Tube, rail, bus, and highway proximity impacts on value.'
  },
  '/how-to-guess-house-prices/schools': {
    title: 'School Zones & Catchments',
    description: 'How ratings and catchments shift demand and pricing.'
  },
  '/how-to-guess-house-prices/ppsft-tactics': {
    title: 'Price-per-Sq-Ft Tactics',
    description: 'Estimating with local price-per-square-foot and adjusting for features.'
  },
  '/how-to-guess-house-prices/mental-comps': {
    title: 'Comps in Your Head',
    description: 'Quick mental comparable analysis using limited info.'
  },
  '/how-to-guess-house-prices/condition-vs-potential': {
    title: 'Condition vs Potential',
    description: 'Weighing fixer-upper discounts versus renovation upside.'
  },
  '/how-to-guess-house-prices/energy-costs': {
    title: 'Energy & Running Costs',
    description: 'EPC, insulation, and heating/cooling types factored into operating costs.'
  },
  '/how-to-guess-house-prices/hoa-fees': {
    title: 'HOA/Service Charges',
    description: 'Dues, amenities, reserves, and special assessments influence.'
  },
  '/how-to-guess-house-prices/climate-risk': {
    title: 'Flood, Fire & Climate Risk',
    description: 'Zones, elevation, materials, and insurance availability.'
  },
  '/how-to-guess-house-prices/newbuild-vs-period': {
    title: 'New-Build vs Period',
    description: 'Premiums, snagging concerns, heritage constraints, and maintenance realities.'
  },
  '/how-to-guess-house-prices/urban-suburban-rural': {
    title: 'Urban vs Suburban vs Rural',
    description: 'Density, car dependence, plot sizes, and amenity trade-offs.'
  },
  '/how-to-guess-house-prices/micro-markets': {
    title: 'Micro-Market Mastery',
    description: 'Learning local price cliffs and street-level pockets.'
  },
  '/how-to-guess-house-prices/reno-costs': {
    title: 'Renovation Cost Quick-Math',
    description: 'Rule-of-thumb costs for kitchens, baths, extensions, and roofs.'
  },
  '/how-to-guess-house-prices/photo-red-flags': {
    title: 'Photo Red Flags',
    description: 'Wide-angle distortions, over-editing, staging tricks, and hidden flaws.'
  },
  '/how-to-guess-house-prices/biases': {
    title: 'Common Guessing Biases',
    description: 'Anchoring, recentness, and prestige bias—and how to counter them.'
  },
  '/how-to-guess-house-prices/practice': {
    title: 'Practice Drills & Worksheets',
    description: 'Downloadable drills to sharpen speed and accuracy.'
  },
  '/property-valuation-guide': {
    title: 'Property Valuation 101',
    description: 'Educational hub on valuation fundamentals and methods.'
  },
  '/property-valuation-guide/comparables': {
    title: 'Comparable Sales Method',
    description: 'Selecting comparable properties and adjusting differences.'
  },
  '/property-valuation-guide/hedonic-pricing': {
    title: 'Hedonic Pricing Basics',
    description: 'Understanding how individual attributes contribute to price.'
  },
  '/property-valuation-guide/avm': {
    title: 'Automated Valuation Models (AVMs)',
    description: 'How AVMs work, their strengths and limitations, and data quality issues.'
  },
  '/property-valuation-guide/cost-approach': {
    title: 'Cost Approach (Rebuild Value)',
    description: 'Land versus improvements, depreciation curves, and rebuild costs.'
  },
  '/property-valuation-guide/income-approach': {
    title: 'Income Approach (Yield/Cap Rate)',
    description: 'Gross rent multiplier, cap rate, and when income methods apply.'
  },
  '/property-valuation-guide/liquidity': {
    title: 'Time on Market & Liquidity',
    description: 'Days-on-market effects, stale listings, and price-cut signals.'
  },
  '/property-valuation-guide/cycles-seasonality': {
    title: 'Market Cycles & Seasonality',
    description: 'Cycles, interest rates, and seasonal demand shifts.'
  },
  '/property-valuation-guide/price-bands': {
    title: 'Price Bands & Psychological Points',
    description: '249,950 versus 250,000; search filters and step changes.'
  },
  '/property-valuation-guide/adjustments': {
    title: 'Adjustments Playbook',
    description: 'Quantifying beds, baths, garages, views, gardens, and parking.'
  },
  '/property-valuation-guide/condition-scales': {
    title: 'Quality & Condition Scales',
    description: 'Standardized grading to compare finish levels.'
  },
  '/property-valuation-guide/uncertainty': {
    title: 'Risk, Uncertainty & Confidence Intervals',
    description: 'Confidence intervals, error bars, and sample size effects.'
  },
  '/property-valuation-guide/bias-fairness': {
    title: 'Bias & Fairness in Valuation',
    description: 'Human and algorithmic bias with mitigation strategies.'
  },
  '/property-valuation-guide/uk-vs-us': {
    title: 'UK vs US Valuation Norms',
    description: 'Contrasts in EPC, leasehold/freehold, MLS, and appraisal standards.'
  },
  '/property-valuation-guide/mortgage-appraisals': {
    title: 'Mortgage Appraisals 101',
    description: 'How lender appraisals work and why they differ from list price.'
  },
  '/property-valuation-guide/tax-vs-market': {
    title: 'Tax Assessment vs Market Value',
    description: 'Assessment methods, appeals, and typical gaps to market.'
  },
  '/property-valuation-guide/surveys-inspections': {
    title: 'Survey & Inspection Types',
    description: 'RICS levels, home inspections, common findings, and impacts.'
  },
  '/property-valuation-guide/data-sources': {
    title: 'Data Sources & Data Hygiene',
    description: 'Where data comes from and how to clean it for accuracy.'
  },
  '/property-valuation-guide/international-metrics': {
    title: 'International Metrics Cheat-Sheet',
    description: 'Square metres vs square feet, lot conventions, and energy labels.'
  },
  '/property-valuation-guide/resources': {
    title: 'Further Reading & Courses',
    description: 'Curated books, papers, and online courses for deeper study.'
  },
  '/pricing-metrics': {
    title: 'Price-per-Sq-Ft & Key Metrics Guide',
    description: 'Hub for every metric used to evaluate listings inside the game.'
  },
  '/pricing-metrics/price-per-sqft': {
    title: 'Price per Square Foot Explained',
    description: 'How to read price per square foot, common pitfalls, and regional baselines.'
  },
  '/pricing-metrics/price-per-sqm': {
    title: 'Price per Square Metre (Intl)',
    description: 'Conversions, rounding rules, and when to prefer square metres over square feet.'
  },
  '/pricing-metrics/lot-size-premiums': {
    title: 'Lot Size & Acreage Premiums',
    description: 'How plot size, frontage, and usable land drive pricing.'
  },
  '/pricing-metrics/bed-bath-adjustments': {
    title: 'Bedroom & Bathroom Adjustments',
    description: 'Typical step-ups for extra bedrooms, bathrooms, and primary suite quality.'
  },
  '/pricing-metrics/condition-renovation-index': {
    title: 'Condition & Renovation Index',
    description: 'Quantifying finish levels and recency of upgrades.'
  },
  '/pricing-metrics/epc-energy-coefficients': {
    title: 'EPC & Energy Score Coefficients',
    description: 'Translating energy ratings into expected pricing effects.'
  },
  '/pricing-metrics/dom-reductions': {
    title: 'Days on Market & Reductions',
    description: 'DOM bands, reduction patterns, and stale listing signals.'
  },
  '/pricing-metrics/list-to-sale-ratio': {
    title: 'List-to-Sale Price Ratio',
    description: 'What percentage over or under list typically looks like by market type.'
  },
  '/pricing-metrics/gross-yield': {
    title: 'Gross Yield & Rent-to-Price',
    description: 'Back-of-the-envelope yield math and investor heuristics.'
  },
  '/pricing-metrics/hoa-service-charges': {
    title: 'HOA/Service Charge Impact',
    description: 'How dues, reserves, and amenities change effective value.'
  },
  '/pricing-metrics/property-tax-impact': {
    title: 'Property/Council Tax Impact',
    description: 'Tax burdens and caps; normalizing cross-region comparisons.'
  },
  '/pricing-metrics/school-index': {
    title: 'School Quality Index Effects',
    description: 'Catchments and rating indices turned into pricing deltas.'
  },
  '/pricing-metrics/transit-walkability': {
    title: 'Transit & Walkability Scores',
    description: 'Proximity to rail, bus, and walk scores modeled as premiums.'
  },
  '/pricing-metrics/view-orientation': {
    title: 'View & Orientation Premiums',
    description: 'Water, park, or skyline views and aspect-driven natural light effects.'
  },
  '/pricing-metrics/parking-garage-ev': {
    title: 'Parking, Garage & EV Charging',
    description: 'Off-street parking tiers, garage counts, and EV-ready perks.'
  },
  '/pricing-metrics/environmental-scores': {
    title: 'Noise/Air Quality Indices',
    description: 'Noise maps, air-quality proxies, and buffer distance discounts.'
  },
  '/pricing-metrics/climate-risk-factors': {
    title: 'Flood/Fire/Climate Risk Factors',
    description: 'Hazard zones, elevation, materials, and insurance cost signals.'
  },
  '/pricing-metrics/new-build-premiums': {
    title: 'New-Build & Developer Premiums',
    description: 'Warranty and amenity premiums versus snagging and size trade-offs.'
  },
  '/pricing-metrics/micro-market-effects': {
    title: 'Micro-Market & Street Effects',
    description: 'Price cliffs at the street or block level and boundary effects.'
  },
  '/game-templates': {
    title: 'Templates: Plug-and-Play Game Packs',
    description: 'Hub listing all curated, one-click duplicate-and-edit game packs.'
  },
  '/game-templates/beginner': {
    title: 'Beginner Starter Pack',
    description: 'Low difficulty, generous hints, and mixed geographies for new players.'
  },
  '/game-templates/hard-mode': {
    title: 'Hard Mode Pack',
    description: 'Tight timers, minimal clues, and expert-only listings.'
  },
  '/game-templates/photo-only': {
    title: 'Photo-Only Visual Pack',
    description: 'Pure image inference with text hidden by design.'
  },
  '/game-templates/text-only': {
    title: 'Text-Only Clues Pack',
    description: 'Specs and descriptions only—no photos to rely on.'
  },
  '/game-templates/uk-victorian-terraces': {
    title: 'UK Victorian Terraces Pack',
    description: 'Period terraces across northern and southern UK cities.'
  },
  '/game-templates/london-zones': {
    title: 'London Zones Pack',
    description: 'Curated sets by Zones 1–6 with micro-market notes.'
  },
  '/game-templates/cornwall-coastal': {
    title: 'Cornwall Coastal Cottages Pack',
    description: 'Sea-view and village cottages with seasonal pricing.'
  },
  '/game-templates/us-suburban-newbuilds': {
    title: 'US Suburban New Builds Pack',
    description: 'Master-planned communities across Sun Belt metros.'
  },
  '/game-templates/nyc-condos': {
    title: 'NYC Condos & Co-ops Pack',
    description: 'Manhattan and Brooklyn condo/co-op mix with fee nuances.'
  },
  '/game-templates/mid-century-modern': {
    title: 'Mid-Century Modern Pack',
    description: 'Bungalows, ramblers, and restored mid-century modern gems.'
  },
  '/game-templates/craftsman-bungalows': {
    title: 'Craftsman & Bungalows Pack',
    description: 'Craftsman-era homes and updated bungalow stock.'
  },
  '/game-templates/international-apartments': {
    title: 'International City Apartments Pack',
    description: 'Compact urban apartments across Europe, Canada, and Australia.'
  },
  '/game-templates/price-band': {
    title: 'Price Band Challenge Pack',
    description: 'Single-band challenges like £250k or $500k across markets.'
  },
  '/game-templates/classroom-econ': {
    title: 'Classroom Econ 101 Pack',
    description: 'Ready-to-teach set aligned to basic economics concepts.'
  },
  '/game-templates/party-offsite': {
    title: 'Party & Offsite Tournament Pack',
    description: 'Lobby-based, bracket-ready pack for groups.'
  },
  '/game-templates/family-friendly': {
    title: 'Family-Friendly Pack',
    description: 'Pre-screened listings and content for mixed-age play.'
  },
  '/game-templates/low-bandwidth': {
    title: 'Low-Bandwidth Pack',
    description: 'Compressed assets and smaller image counts.'
  },
  '/game-templates/accessibility': {
    title: 'Accessibility-Optimized Pack',
    description: 'High-contrast, alt-text rich, and keyboard-friendly templates.'
  },
  '/game-templates/realtor-leadgen': {
    title: 'Realtor Lead-Gen Pack',
    description: 'CTA-ready pack with share assets and capture flows.'
  },
  '/for-estate-agents': {
    title: 'For Estate Agents & Brokers',
    description: 'Hub with benefits, use cases, and demo calls to action for property pros.'
  },
  '/for-estate-agents/lead-magnets': {
    title: 'Lead Magnet Quiz Playbooks',
    description: 'Proven quiz funnels for valuations and buyer registrations.'
  },
  '/for-estate-agents/open-house': {
    title: 'Open House Game Stations',
    description: 'Kiosk or projector setups to engage walk-ins and capture details.'
  },
  '/for-estate-agents/embeds': {
    title: 'Website Embed Guide',
    description: 'Add the game to your site with responsive, SEO-safe embeds.'
  },
  '/for-estate-agents/crm-integrations': {
    title: 'CRM & Email Integrations',
    description: 'Sync leads to HubSpot, Mailchimp, Salesforce, and auto-segment them.'
  },
  '/for-estate-agents/social-kits': {
    title: 'Social Media Campaign Kits',
    description: 'Plug-and-play posts, stories, and reels with schedule templates.'
  },
  '/for-estate-agents/roi-calculator': {
    title: 'ROI & Conversion Calculator',
    description: 'Estimate signups, appointments, and cost per lead.'
  },
  '/for-estate-agents/white-label': {
    title: 'White-Label & Branding Guide',
    description: 'Logos, colors, watermarks, and co-branding options.'
  },
  '/for-estate-agents/gdpr-privacy': {
    title: 'Consent, GDPR & Privacy',
    description: 'Lawful basis, consent text samples, and data subject request workflows.'
  },
  '/for-estate-agents/multi-branch': {
    title: 'Multi-Branch Management',
    description: 'Roles, permissions, and roll-up reporting for large offices.'
  },
  '/for-estate-agents/team-leaderboards': {
    title: 'Team Leaderboards & Incentives',
    description: 'Sales-team competitions and prize structures.'
  },
  '/for-estate-agents/listing-presentations': {
    title: 'Listing Presentation Enhancer',
    description: 'Use games to teach sellers pricing realism and provide leave-behind links.'
  },
  '/for-estate-agents/community-events': {
    title: 'Community Outreach & Events',
    description: 'Fairs, sponsorships, and local market education nights.'
  },
  '/for-estate-agents/agent-training': {
    title: 'Training New Agents with Games',
    description: 'Price-intuition drills and micro-assessments for rookies.'
  },
  '/for-estate-agents/case-study-boutique': {
    title: 'Case Study: Boutique Agency',
    description: 'How a 5–15 person shop uses the platform end to end.'
  },
  '/for-estate-agents/case-study-franchise': {
    title: 'Case Study: Franchise Network',
    description: 'Multi-city deployment, governance, and brand consistency.'
  },
  '/for-estate-agents/sponsorships': {
    title: 'Sponsorships & Co-Branded Packs',
    description: 'Partner with lenders, insurers, or developers.'
  },
  '/for-estate-agents/api-sso': {
    title: 'API & SSO for Brokerages',
    description: 'JWT/SSO, webhooks, and secure leaderboards for portals.'
  },
  '/for-estate-agents/testimonials-toolkit': {
    title: 'Testimonials Collection Toolkit',
    description: 'Scripts and automations to capture client quotes.'
  },
  '/for-estate-agents/support': {
    title: 'Troubleshooting & Support (Agents)',
    description: 'Common setup issues and concierge support options.'
  },
  '/for-classrooms': {
    title: 'For Teachers & Classrooms',
    description: 'Lesson plans, safety controls, and deployment guides for using the game in education.'
  },
  '/for-classrooms/safety-moderation': {
    title: 'Safety & Moderation Controls',
    description: 'Tools to ensure age-appropriate content and manage classroom interactions.'
  },
  '/for-classrooms/uk-curriculum': {
    title: 'UK Curriculum Alignment',
    description: 'Mapping game activities to UK curriculum objectives.'
  },
  '/for-classrooms/us-standards': {
    title: 'US Standards Alignment',
    description: 'Aligning game activities with US educational standards.'
  },
  '/for-classrooms/lesson-ages-8-11': {
    title: 'Lesson Plans: Ages 8–11',
    description: 'Structured lessons introducing basic property concepts for young learners.'
  },
  '/for-classrooms/lesson-ages-12-14': {
    title: 'Lesson Plans: Ages 12–14',
    description: 'Explore regional differences and budgeting through guided play.'
  },
  '/for-classrooms/lesson-ages-15-18': {
    title: 'Lesson Plans: Ages 15–18',
    description: 'Deep dives into valuation, comps, and economic factors.'
  },
  '/for-classrooms/assessment-rubrics': {
    title: 'Assessment Rubrics & Marking',
    description: 'Evaluate student performance with built-in rubrics and marking guides.'
  },
  '/for-classrooms/export-grades': {
    title: 'Export Grades & Reports',
    description: 'Generate reports or CSV exports for gradebooks.'
  },
  '/for-classrooms/tournaments': {
    title: 'Classroom Tournaments',
    description: 'Organize intra-class competitions with automatic scoring.'
  },
  '/for-classrooms/worksheets': {
    title: 'Printable Worksheets',
    description: 'Downloadable worksheets to reinforce learning offline.'
  },
  '/for-classrooms/remote-hybrid': {
    title: 'Remote/Hybrid Setup',
    description: 'Run sessions across in-person and remote students seamlessly.'
  },
  '/for-classrooms/cross-curricular': {
    title: 'Cross-Curricular Projects',
    description: 'Projects combining economics, geography, and maths.'
  },
  '/for-classrooms/sen-adaptations': {
    title: 'SEN/Accessibility Adaptations',
    description: 'Adjustments for special educational needs and accessibility.'
  },
  '/for-classrooms/parent-consent': {
    title: 'Parent Letters & Consent',
    description: 'Template letters and consent forms for guardians.'
  },
  '/for-classrooms/data-privacy': {
    title: 'Data Privacy for Schools',
    description: 'Guidance on student data handling and privacy compliance.'
  },
  '/for-classrooms/community': {
    title: 'Teacher Community & Sharing',
    description: 'Share lesson ideas and download packs from other educators.'
  },
  '/for-classrooms/webinars': {
    title: 'Teacher Training Webinars',
    description: 'Live and recorded sessions to help educators get started.'
  },
  '/for-classrooms/it-deployment': {
    title: 'School IT Deployment Guide',
    description: 'Network requirements, whitelisting, and device setup tips.'
  },
  '/for-classrooms/faq-troubleshooting': {
    title: 'FAQ & Troubleshooting (Schools)',
    description: 'Answers to common questions and classroom-specific support.'
  },
  '/for-events': {
    title: 'Parties, Offsites & Team-Building',
    description: 'Hub for event formats, kits, and host tools.'
  },
  '/for-events/icebreakers': {
    title: 'Icebreaker Kits',
    description: 'Quick-start icebreakers for small or large groups.'
  },
  '/for-events/offsite-playbook': {
    title: 'Offsite Playbook',
    description: 'End-to-end playbook for company offsites and retreats.'
  },
  '/for-events/remote-night': {
    title: 'Remote Team Game Night',
    description: 'Run a virtual session with latency-friendly settings.'
  },
  '/for-events/hybrid-setup': {
    title: 'Hybrid Event Setup',
    description: 'Mix in-room and remote players with fair rules.'
  },
  '/for-events/tournaments': {
    title: 'Tournament Brackets & Seeding',
    description: 'Single or double elimination formats with automatic seeding.'
  },
  '/for-events/scoreboard': {
    title: 'Scoreboard Display Mode',
    description: 'Projector-friendly big-board with live updates.'
  },
  '/for-events/av-guide': {
    title: 'AV & Projector Guide',
    description: 'Room layout, audio, casting, and adapter tips.'
  },
  '/for-events/slack': {
    title: 'Slack App for Events',
    description: 'Create lobbies, post scores, and recap games in Slack.'
  },
  '/for-events/teams-zoom': {
    title: 'Teams/Zoom Runbook',
    description: 'Breakouts, screen sharing, co-host flow, and recording tips.'
  },
  '/for-events/rsvp-calendar': {
    title: 'RSVP & Calendar Flow',
    description: 'Collect RSVPs, send reminders, and add calendar holds.'
  },
  '/for-events/lobby-checkin': {
    title: 'Lobby & Check-In Management',
    description: 'Waiting room options, late joins, and player caps.'
  },
  '/for-events/host-guide': {
    title: 'Host & Moderator Guide',
    description: 'Roles, scripts, dispute handling, and pacing cues.'
  },
  '/for-events/prizes': {
    title: 'Prizes & Incentives Toolkit',
    description: 'Prize ideas, budget tiers, and sponsor tie-ins.'
  },
  '/for-events/code-of-conduct': {
    title: 'Event Code of Conduct',
    description: 'Expectations, safety guidelines, and escalation steps.'
  },
  '/for-events/photo-consent': {
    title: 'Photo/Video Consent',
    description: 'Template consent forms and recording guidance.'
  },
  '/for-events/printables': {
    title: 'Printable Scorecards & Badges',
    description: 'PDFs for badges, scorecards, and brackets.'
  },
  '/for-events/corporate-branding': {
    title: 'Corporate Branding at Events',
    description: 'Co-branding, sponsor panels, and signage assets.'
  },
  '/for-events/sponsor-activation': {
    title: 'Sponsor Activation Ideas',
    description: 'Booth games, QR hunts, and leaderboard takeovers.'
  },
  '/for-events/run-of-show': {
    title: 'Sample Run-of-Show Schedules',
    description: '30, 60, and 90-minute agendas for different group sizes.'
  },
  '/for-events/case-studies': {
    title: 'Event Case Studies Library',
    description: 'Real-world examples with outcomes and tips.'
  },
  '/data-sources-licensing': {
    title: 'Data Sources, Licensing & Fair Use',
    description: 'Overview of sourcing, rights, and policies in plain English.'
  },
  '/data-sources-licensing/listing-sources': {
    title: 'Listing Sources Overview',
    description: 'What listing links are supported and how they’re used.'
  },
  '/data-sources-licensing/image-licensing': {
    title: 'Image Licensing 101',
    description: 'Rights basics for creators with do’s and don’ts.'
  },
  '/data-sources-licensing/attribution': {
    title: 'Attribution Requirements',
    description: 'Where, when, and how to credit sources properly.'
  },
  '/data-sources-licensing/fair-use': {
    title: 'Fair Use & Educational Use',
    description: 'When limited preview may qualify for fair use with examples.'
  },
  '/data-sources-licensing/uk-portals': {
    title: 'UK Portal Terms Summary',
    description: 'Rightmove and Zoopla high-level do’s and don’ts.'
  },
  '/data-sources-licensing/us-portals': {
    title: 'US Portal Terms Summary',
    description: 'Zillow and Realtor.com high-level do’s and don’ts.'
  },
  '/data-sources-licensing/robots-caching': {
    title: 'Robots.txt & Rate Limits',
    description: 'Respecting robots, caching rules, and throttling.'
  },
  '/data-sources-licensing/caching-hotlinking': {
    title: 'Caching & Hotlinking Policy',
    description: 'What’s cached, expiry times, and hotlink etiquette.'
  },
  '/data-sources-licensing/ugc-responsibilities': {
    title: 'UGC: Creator Responsibilities',
    description: 'Creator warranties, takedowns, and accuracy notes.'
  },
  '/data-sources-licensing/dmca': {
    title: 'DMCA & Takedown Flow',
    description: 'How rights owners can request removal and timelines.'
  },
  '/data-sources-licensing/rights-verification': {
    title: 'Rights Owner Verification',
    description: 'Proving ownership and authorized representative status.'
  },
  '/data-sources-licensing/personal-data': {
    title: 'Personal Data in Listings',
    description: 'Handling faces, plates, and addresses with redaction guidance.'
  },
  '/data-sources-licensing/screenshot-vs-download': {
    title: 'Screenshot vs Download',
    description: 'Allowed preview methods and quality guidance.'
  },
  '/data-sources-licensing/watermarks': {
    title: 'Watermarks & Credit Placement',
    description: 'Placement rules and readability standards.'
  },
  '/data-sources-licensing/api-embed': {
    title: 'API/Embed vs Scraping Policy',
    description: 'Preferred methods and prohibited automation.'
  },
  '/data-sources-licensing/international': {
    title: 'International Jurisdictions',
    description: 'High-level notes for EU, Canada, Australia, and more.'
  },
  '/data-sources-licensing/disallowed': {
    title: 'Disallowed Content Types',
    description: 'Content that cannot be uploaded or linked with examples.'
  },
  '/data-sources-licensing/removal-requests': {
    title: 'Content Removal Requests',
    description: 'User-initiated removals and review steps.'
  },
  '/data-sources-licensing/transparency': {
    title: 'Transparency Reports',
    description: 'Periodic stats on takedowns and policy actions.'
  },
  '/data-sources-licensing/legal-faq': {
    title: 'Legal FAQ (Not Legal Advice)',
    description: 'Common questions answered plainly with disclaimers.'
  },
  '/privacy': {
    title: 'Privacy, Security & Parental Info',
    description: 'Plain-English overview with links to detailed policies and safety info.'
  },
  '/privacy/cookies': {
    title: 'Cookie Policy',
    description: 'What cookies we set, why, and the choices available to you.'
  },
  '/privacy/data-retention': {
    title: 'Data Retention & Deletion',
    description: 'Retention schedules and how to delete your data.'
  },
  '/privacy/parents': {
    title: 'Age Guidance & Parental Info',
    description: 'Age suitability, supervision tips, and parental controls.'
  },
  '/privacy/child-safety': {
    title: 'Child Safety & Reporting',
    description: 'Safety features and how to report concerns.'
  },
  '/privacy/account-security': {
    title: 'Account Security & 2FA',
    description: 'Passwords, passkeys, and two-factor options.'
  },
  '/privacy/sso-passwordless': {
    title: 'SSO & Passwordless Options',
    description: 'Google and Apple single sign-on plus magic link logins.'
  },
  '/privacy/encryption': {
    title: 'Encryption & Storage',
    description: 'Encryption at rest and in transit with key management notes.'
  },
  '/privacy/incident-response': {
    title: 'Incident Response & Status',
    description: 'How we handle incidents and link to the status page.'
  },
  '/privacy/vulnerability-disclosure': {
    title: 'Vulnerability Disclosure',
    description: 'How to report security issues responsibly.'
  },
  '/privacy/bug-bounty': {
    title: 'Bug Bounty Program',
    description: 'Scope, rewards, and rules for researchers.'
  },
  '/privacy/gdpr-rights': {
    title: 'GDPR: Your Rights',
    description: 'Access, rectification, portability, erasure, and objection rights.'
  },
  '/privacy/ccpa-rights': {
    title: 'CCPA/CPRA: Your Rights',
    description: 'California and related state privacy rights.'
  },
  '/privacy/consent-management': {
    title: 'Consent Management (CMP)',
    description: 'Manage, withdraw, and update your consent preferences.'
  },
  '/privacy/processors': {
    title: 'Third-Party Processors',
    description: 'List of vendors and purposes for data processing.'
  },
  '/privacy/analytics-choices': {
    title: 'Analytics & Tracking Choices',
    description: 'Opt-in or out for analytics and personalization.'
  },
  '/privacy/ads-disclosures': {
    title: 'Ads & Sponsorship Disclosures',
    description: 'Ad labeling, affiliate links, and sponsor policies.'
  },
  '/privacy/chat-rules': {
    title: 'Safe Usernames & Chat Rules',
    description: 'Community safety guidelines and naming rules.'
  },
  '/privacy/accessibility': {
    title: 'Accessibility & Privacy',
    description: 'Screen reader support, captions, and private modes.'
  },
  '/privacy/contact': {
    title: 'Contact: DPO & Security',
    description: 'How to reach the privacy or security team.'
  },
  '/privacy/change-log': {
    title: 'Policy Change Log',
    description: 'Versioned history of policy updates.'
  },
  '/blog': {
    title: 'Product Updates & Blog',
    description: 'Main blog hub with filters and search for updates and community stories.'
  },
  '/blog/changelog': {
    title: 'Changelog & Release Notes',
    description: 'Versioned updates with highlights and fixes.'
  },
  '/blog/roadmap': {
    title: 'Roadmap & What’s Next',
    description: 'Public roadmap and prioritization notes.'
  },
  '/blog/feature-deep-dives': {
    title: 'Feature Deep Dives',
    description: 'Long-form posts that explore major features in detail.'
  },
  '/blog/tutorials': {
    title: 'How-To Tutorials & Tips',
    description: 'Step-by-step guides and pro tips for players and creators.'
  },
  '/blog/creator-spotlights': {
    title: 'Creator Spotlights',
    description: 'Interviews with standout community creators.'
  },
  '/blog/game-of-the-week': {
    title: 'Community Game of the Week',
    description: 'Weekly pick with commentary and stats.'
  },
  '/blog/case-studies': {
    title: 'Case Studies & Success Stories',
    description: 'Results from classrooms, agencies, and teams.'
  },
  '/blog/market-briefs': {
    title: 'Market Insight Briefs',
    description: 'Bite-size property market insights for players.'
  },
  '/blog/guessing-trends': {
    title: 'Data Stories: Guessing Trends',
    description: 'Analysis of anonymized guess data and accuracy trends.'
  },
  '/blog/performance': {
    title: 'Performance & Reliability Updates',
    description: 'Infrastructure improvements, uptime notes, and scaling lessons.'
  },
  '/blog/integrations': {
    title: 'Integrations & Partnerships',
    description: 'New integrations and partner announcements.'
  },
  '/blog/events-recaps': {
    title: 'Events & Webinar Recaps',
    description: 'Summaries, slides, and recordings from recent events.'
  },
  '/blog/seasonal-events': {
    title: 'Seasonal Events & Special Modes',
    description: 'Holiday or special edition game announcements.'
  },
  '/blog/educator-corner': {
    title: 'Educator Corner',
    description: 'Posts specifically for teachers and schools.'
  },
  '/blog/agent-corner': {
    title: 'Agent/Broker Corner',
    description: 'Posts specifically for property professionals.'
  },
  '/blog/engineering': {
    title: 'Behind the Scenes (Engineering)',
    description: 'Architecture, testing, and build pipeline insights.'
  },
  '/blog/design-ux': {
    title: 'Design & UX Decisions',
    description: 'Design rationale, accessibility choices, and UI patterns.'
  },
  '/blog/ab-tests': {
    title: 'A/B Tests & Learnings',
    description: 'Experiments, results, and what changed.'
  },
  '/blog/beta-programs': {
    title: 'Beta Programs & Call for Testers',
    description: 'Sign-ups and eligibility for upcoming betas.'
  },
  '/blog/press': {
    title: 'Press Kit & Announcements',
    description: 'Press assets and major company news.'
  }
};
